"use server";

import { revalidatePath } from 'next/cache';
import {
  smsService,
  smsManagementService,
  smsLoggingService,
  smsBalanceService
} from '../services';
import { smsPricingService } from '../services/sms-pricing.service';
import { TenantAwareDB } from '../db';
import { getServerTenantId, getServerUserId } from '../tenant-utils-server';
import logger from '@/lib/logger';

// SMS Configuration Actions
export async function getSmsConfigurations() {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    const result = await smsManagementService().getSmsConfigurations(
      userId?.toString(),
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("getSmsConfigurations error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to get SMS configurations");
    }
    
    return result.data;
  } catch (error) {
    logger.error("Error getting SMS configurations:", { error });
    throw error;
  }
}

export async function getActiveSmsConfiguration() {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    const result = await smsManagementService().getActiveSmsConfiguration(
      userId?.toString(),
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("getActiveSmsConfiguration error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to get active SMS configuration");
    }
    
    return result.data;
  } catch (error) {
    logger.error("Error getting active SMS configuration:", { error });
    throw error;
  }
}

export async function createSmsConfiguration(data: {
  pendingPaymentTemplate: string;
  overduePaymentTemplate: string;
  pendingReminderDays: number[];
  overdueReminderDays: number[];
}) {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    const result = await smsManagementService().createSmsConfiguration(
      data,
      userId?.toString(),
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("createSmsConfiguration error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to create SMS configuration");
    }
    
    revalidatePath('/sms/configuration');
    return { success: true, data: result.data };
  } catch (error) {
    logger.error("Error creating SMS configuration:", { error });
    throw error;
  }
}

export async function updateSmsConfiguration(id: string, data: {
  pendingPaymentTemplate?: string;
  overduePaymentTemplate?: string;
  pendingReminderDays?: number[];
  overdueReminderDays?: number[];
}) {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    const result = await smsManagementService().updateSmsConfiguration(
      id,
      data,
      userId?.toString(),
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("updateSmsConfiguration error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to update SMS configuration");
    }
    
    revalidatePath('/sms/configuration');
    return { success: true, data: result.data };
  } catch (error) {
    logger.error("Error updating SMS configuration:", { error });
    throw error;
  }
}

export async function activateSmsConfiguration(id: string) {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();

    if (!id) {
      // Deactivate all configurations
      const result = await smsManagementService().deactivateAllSmsConfigurations(
        userId?.toString(),
        tenantId || undefined
      );

      if (!result.success) {
        logger.error("deactivateAllSmsConfigurations error:", { error: result.error });
        throw new Error(result.error?.userMessage || "Failed to deactivate SMS configurations");
      }
    } else {
      // Activate specific configuration
      const result = await smsManagementService().activateSmsConfiguration(
        id,
        userId?.toString(),
        tenantId || undefined
      );

      if (!result.success) {
        logger.error("activateSmsConfiguration error:", { error: result.error });
        throw new Error(result.error?.userMessage || "Failed to activate SMS configuration");
      }
    }

    revalidatePath('/sms/configuration');
    return { success: true };
  } catch (error) {
    logger.error("Error with SMS configuration activation:", { error });
    throw error;
  }
}

// SMS Sending Actions
export async function sendPaymentReminderSms(data: {
  paymentIds: string[];
  templateType: 'pending' | 'overdue';
  customTemplate?: string;
  combinePayments?: boolean;
}) {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    const result = await smsService().sendPaymentReminderSms(
      data,
      userId?.toString(),
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("sendPaymentReminderSms error:", { error: result.error });
      if(result.error?.details && result.error.details.rule){
        return { success: false, error: result.error?.details?.rule , errorType: result.error?.cause?.name };
      }
      return { success: false, error: result.error?.userMessage || 'Failed to send payment reminder SMS' , errorType: 'general' };
    }else{
      revalidatePath('/payments');
      revalidatePath('/sms/logs');
      return { success: true, data: result.data };
    }
  } catch (error) {
    logger.error("Error sending payment reminder SMS:", { error });
    return { success: false, error: error instanceof Error ? error.message : "Failed to send payment reminder SMS", errorType: 'general' };
  }
}

export async function sendTeamSms(data: {
  teamId: string;
  message: string;
  athleteIds?: string[];
}) {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    const result = await smsService().sendTeamSms(
      data,
      userId?.toString(),
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("sendTeamSms error:", { error: result.error });
      if(result.error?.details && result.error.details.rule){
        return { success: false, error: result.error?.details?.rule , errorType: result.error?.cause?.name };
      }
      return { success: false, error: result.error?.userMessage || 'Failed to send team SMS' , errorType: 'general' };
    }else{
      revalidatePath('/teams');
      revalidatePath('/sms/logs');
      return { success: true, data: result.data };
    }
  } catch (error) {
    logger.error("Error sending team SMS:", { error });
    return { success: false, error: error instanceof Error ? error.message : "Failed to send team SMS", errorType: 'general' };
  }
}

// SMS Logs Actions
export async function getSmsLogs() {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    const result = await smsLoggingService().getSmsLogs(
      userId?.toString(),
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("getSmsLogs error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to get SMS logs");
    }
    
    return result.data;
  } catch (error) {
    logger.error("Error getting SMS logs:", { error });
    throw error;
  }
}

export async function getSmsLogsPaginated(options: {
  page: number;
  limit: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filters?: {
    type?: string;
    status?: string;
    senderType?: string;
    dateFrom?: string;
    dateTo?: string;
  };
}) {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    const result = await smsLoggingService().getSmsLogsPaginated(
      options.page,
      options.limit,
      options.search,
      options.sortBy,
      options.sortOrder,
      options.filters,
      userId?.toString(),
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("getSmsLogsPaginated error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to get SMS logs");
    }
    
    return result.data;
  } catch (error) {
    logger.error("Error getting paginated SMS logs:", { error });
    throw error;
  }
}

export async function getSmsStats(dateFrom?: Date, dateTo?: Date) {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    const result = await smsLoggingService().getSmsStats(
      dateFrom,
      dateTo,
      userId?.toString(),
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("getSmsStats error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to get SMS statistics");
    }
    
    return result.data;
  } catch (error) {
    logger.error("Error getting SMS statistics:", { error });
    throw error;
  }
}

// SMS Balance Actions
export async function getSmsBalance() {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    const result = await smsBalanceService().getSmsBalance(
      userId?.toString(),
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("getSmsBalance error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to get SMS balance");
    }
    
    return result.data;
  } catch (error) {
    logger.error("Error getting SMS balance:", { error });
    throw error;
  }
}

export async function getBalanceStatus() {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    const result = await smsBalanceService().getBalanceStatus(
      userId?.toString(),
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("getBalanceStatus error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to get balance status");
    }
    
    return result.data;
  } catch (error) {
    logger.error("Error getting balance status:", { error });
    throw error;
  }
}

// SMS Pricing Actions
export async function calculateSmsPrice(credits: number) {
  try {
    const result = await smsPricingService().calculatePurchasePrice(credits);

    if (!result.success) {
      logger.error("calculateSmsPrice error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to calculate SMS price");
    }

    return { success: true, data: result.data };
  } catch (error) {
    logger.error("Error calculating SMS price:", { error });
    throw error;
  }
}

export async function getSmsPricingTiers() {
  try {
    const result = await smsPricingService().getPricingTiers();

    if (!result.success) {
      logger.error("getSmsPricingTiers error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to get pricing tiers");
    }

    return { success: true, data: result.data };
  } catch (error) {
    logger.error("Error getting SMS pricing tiers:", { error });
    throw error;
  }
}

export async function purchaseSmsCredits(data: {
  credits: number;
  expectedPrice: number;
  paymentData: {
    cardNumber: string;
    expiryDate: string;
    cvv: string;
    cardHolder: string;
  };
}) {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();

    // Validate purchase server-side
    const validation = await smsPricingService().validatePurchase(data.credits, data.expectedPrice);

    if (!validation.success) {
      logger.error("purchaseSmsCredits validation error:", { error: validation.error });
      throw new Error(validation.error?.userMessage || "Purchase validation failed");
    }

    // TODO: In production, integrate with real payment processor here
    // For now, we'll simulate a successful payment

    // Add credits to balance
    const result = await smsBalanceService().addSmsCredits(
      data.credits,
      userId?.toString(),
      tenantId || undefined
    );

    if (!result.success) {
      logger.error("purchaseSmsCredits addCredits error:", { error: result.error });
      if(result.error?.details && result.error.details.rule){
        return { success: false, error: result.error?.details?.rule , errorType: result.error?.cause?.name };
      }
      return { success: false, error: result.error?.userMessage || 'Failed to add SMS credits' , errorType: 'general' };
    }else{
      revalidatePath('/sms/balance');
      return { success: true, data: result.data };
    }
  } catch (error) {
    logger.error("Error purchasing SMS credits:", { error });
    return { success: false, error: error instanceof Error ? error.message : "Failed to purchase SMS credits", errorType: 'general' };
  }
}

// Get payments with athlete details for SMS reminders
export async function getPaymentsForSms() {
  try {
    const tenantId = await getServerTenantId();

    // Get both pending and overdue payments with athlete details
    const [pendingPayments, overduePayments] = await Promise.all([
      TenantAwareDB.getPendingPaymentsWithAthleteDetails(tenantId || undefined),
      TenantAwareDB.getOverduePaymentsWithAthleteDetails(tenantId || undefined)
    ]);

    // Combine and return all payments
    const allPayments = [...pendingPayments, ...overduePayments];

    return { success: true, data: allPayments };
  } catch (error) {
    logger.error("Error getting payments for SMS:", { error });
    throw error;
  }
}
