import { BaseService } from './base';
import { ServiceResult, ValidationError } from '../errors/types';
import { NotFoundError, BusinessRuleError } from '../errors/errors';
import { TenantAwareDB } from '../db';
import { getServerTenantId, getServerUserId } from '../tenant-utils-server';
import { createAthleteWithTeamAssignments } from '@/lib/actions';
import { createPayment } from '@/lib/actions';
import { validators } from './validation';
import { getPaymentTranslation, serverTranslate } from '../utils/server-translation';
import {t} from "i18next";
import logger from '@/lib/logger';

export interface CreateAthleteData {
  name: string;
  surname: string;
  nationalId?: string;
  birthDate?: string;
  registrationDate?: string;
  parentName?: string;
  parentSurname?: string;
  parentPhone?: string;
  parentEmail?: string;
  parentAddress?: string;
}

export interface UpdateAthleteData {
  name?: string;
  surname?: string;
  nationalId?: string;
  birthDate?: string;
  registrationDate?: string;
  parentName?: string;
  parentSurname?: string;
  parentPhone?: string;
  parentEmail?: string;
  parentAddress?: string;
  status?: 'active' | 'inactive' | 'suspended';
}

export interface BulkImportAthleteData {
  name: string;
  surname: string;
  nationalId: string;
  birthDate: string;
  registrationDate: string;
  parentName: string;
  parentSurname: string;
  parentPhone: string;
  parentEmail?: string;
  currentBalance?: string;
  currentBalanceLastPaymentDate?: Date | string;
  status?: string;
  teamAssignments: Array<{
    teamId: string;
    paymentPlanId?: string;
  }>;
}

export interface BulkImportResult {
  success: boolean;
  processed: number;
  created: number;
  errors: Array<{
    row: number;
    message: string;
  }>;
}

export interface AthleteFilters {
  name?: string;
  surname?: string;
  status?: 'active' | 'inactive' | 'suspended';
  schoolId?: string;
  teamId?: string;
  page?: number;
  limit?: number;
}

export class AthleteService extends BaseService {
  constructor() {
    super('AthleteService');
  }

  /**
   * Get all athletes with optional filtering
   */
  async getAthletes(
    filters?: AthleteFilters,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getAthletes',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getAthletes(effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'athletes',
        metadata: { filters },
      }
    );
  }

  /**
   * Get athletes with pagination
   */
  async getAthletesPaginated(
    page: number = 1,
    limit: number = 10,
    search?: string,
    sortBy?: string,
    sortOrder?: 'asc' | 'desc',
    filters?: {
      name?: string;
      surname?: string;
      parentEmail?: string;
      parentPhone?: string;
      nationalId?: string;
      status?: 'active' | 'inactive' | 'suspended';
    },
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'getAthletesPaginated',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getAthletesPaginated(effectiveTenantId || undefined, {
          page,
          limit,
          search,
          sortBy,
          sortOrder,
          filters,
        });
      },
      {
        userId,
        tenantId,
        resource: 'athletes',
        metadata: { page, limit, search, sortBy, sortOrder, filters },
      }
    );
  }

  /**
   * Get athlete by ID
   */
  async getAthleteById(
    id: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.checkResourceExists(
      'getAthleteById',
      'Athlete',
      id,
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getAthleteById(id, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'athlete',
      }
    );
  }

  /**
   * Create a new athlete
   */
  async createAthlete(
    data: CreateAthleteData,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    // Define validation functions for this specific operation
    const validationFunctions = [
      (data: CreateAthleteData): ValidationError | null => {
        if (!data.name || data.name.trim().length === 0) {
          return { field: 'name', message: 'name_required_validation', code: 'REQUIRED_FIELD_MISSING' };
        }
        if (data.name.length < 2 || data.name.length > 50) {
          return { field: 'name', message: 'name_length_validation', code: 'INVALID_LENGTH' };
        }
        return null;
      },
      (data: CreateAthleteData): ValidationError | null => {
        if (!data.surname || data.surname.trim().length === 0) {
          return { field: 'surname', message: 'surname_required_validation', code: 'REQUIRED_FIELD_MISSING' };
        }
        if (data.surname.length < 2 || data.surname.length > 50) {
          return { field: 'surname', message: 'surname_length_validation', code: 'INVALID_LENGTH' };
        }
        return null;
      },
      (data: CreateAthleteData): ValidationError | null => {
        if (data.parentEmail) {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(data.parentEmail)) {
            return { field: 'parentEmail', message: 'email_format_validation', code: 'INVALID_FORMAT' };
          }
        }
        return null;
      },
      (data: CreateAthleteData): ValidationError | null => {
        return validators.nationalId('nationalId', "national_id_validation", t)(data.nationalId ?? '');
      },
    ];

    return this.executeWithValidation(
      'createAthlete',
      data,
      validationFunctions,
      async (validatedData) => {
        // Check for duplicate national ID if provided
        if (validatedData.nationalId) {
          const existingAthlete = await this.findAthleteByNationalId(validatedData.nationalId, userId, tenantId);
          if (existingAthlete && existingAthlete.error?.code !== 'RESOURCE_NOT_FOUND') {
            throw new BusinessRuleError(
              'unique_national_id',
              `Athlete with national ID ${validatedData.nationalId} already exists`,
              undefined,
              'An athlete with this national ID already exists in the system.'
            );
          }
        }

        // Convert to the format expected by TenantAwareDB
        const athleteData = {
          name: validatedData.name,
          surname: validatedData.surname,
          nationalId: validatedData.nationalId || '',
          birthDate: validatedData.birthDate || '',
          registrationDate: validatedData.registrationDate || new Date().toISOString().split('T')[0],
          parentName: validatedData.parentName || '',
          parentSurname: validatedData.parentSurname || '',
          parentPhone: validatedData.parentPhone || '',
          parentEmail: validatedData.parentEmail || '',
          parentAddress: validatedData.parentAddress || '',
        };

        return TenantAwareDB.createAthlete(athleteData);
      },
      {
        userId,
        tenantId,
        resource: 'athlete',
        metadata: { operation: 'create' },
      }
    );
  }

  /**
   * Update an athlete
   */
  async updateAthlete(
    id: string,
    data: UpdateAthleteData,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    const validationFunctions = [
      (data: UpdateAthleteData): ValidationError | null => {
        if (data.name !== undefined && (!data.name || data.name.trim().length === 0)) {
          return { field: 'name', message: 'name_required_validation', code: 'REQUIRED_FIELD_MISSING' };
        }
        if (data.name && (data.name.length < 2 || data.name.length > 50)) {
          return { field: 'name', message: 'name_length_validation', code: 'INVALID_LENGTH' };
        }
        return null;
      },
      (data: UpdateAthleteData): ValidationError | null => {
        if (data.surname !== undefined && (!data.surname || data.surname.trim().length === 0)) {
          return { field: 'surname', message: 'surname_required_validation', code: 'REQUIRED_FIELD_MISSING' };
        }
        if (data.surname && (data.surname.length < 2 || data.surname.length > 50)) {
          return { field: 'surname', message: 'surname_length_validation', code: 'INVALID_LENGTH' };
        }
        return null;
      },
      (data: UpdateAthleteData): ValidationError | null => {
        if (data.parentEmail) {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(data.parentEmail)) {
            return { field: 'parentEmail', message: 'email_format_validation', code: 'INVALID_FORMAT' };
          }
        }
        return null;
      },
      (data: UpdateAthleteData): ValidationError | null => {
        return validators.nationalId('nationalId', "national_id_validation", t)(data.nationalId ?? '');
      },
    ];

    return this.executeWithValidation(
      'updateAthlete',
      data,
      validationFunctions,
      async (validatedData) => {
        // Check if athlete exists
        const existingAthlete = await this.getAthleteById(id, userId, tenantId);
        if (!existingAthlete.success) {
          throw new NotFoundError('Athlete', id);
        }

        return TenantAwareDB.updateAthlete(id, validatedData);
      },
      {
        userId,
        tenantId,
        resource: 'athlete',
        metadata: { operation: 'update', athleteId: id },
      }
    );
  }

  /**
   * Delete an athlete
   */
  async deleteAthlete(
    id: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeOperation(
      'deleteAthlete',
      async () => {
        // Check if athlete exists
        const existingAthlete = await this.getAthleteById(id, userId, tenantId);
        if (!existingAthlete.success) {
          throw new NotFoundError('Athlete', id);
        }

        // Check for business rule violations
        const hasActivePayments = await this.checkAthleteHasActivePayments(id, userId, tenantId);
        if (hasActivePayments && hasActivePayments.error?.code !== 'RESOURCE_NOT_FOUND') {
          throw new BusinessRuleError(
            'cannot_delete_athlete_with_active_payments',
            'Cannot delete athlete with active payments',
            undefined,
            'This athlete cannot be deleted because they have active payments. Please resolve all payments first.'
          );
        }

        await TenantAwareDB.deleteAthlete(id);
        return true;
      },
      {
        userId,
        tenantId,
        resource: 'athlete',
        metadata: { operation: 'delete', athleteId: id },
      }
    );
  }

  /**
   * Get overdue athletes
   */
  async getOverdueAthletes(
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getOverdueAthletes',
      async () => {
        return TenantAwareDB.getOverdueAthletes();
      },
      {
        userId,
        tenantId,
        resource: 'athletes',
        metadata: { filter: 'overdue' },
      }
    );
  }

  /**
   * Private helper methods
   */
  private async findAthleteByNationalId(
    nationalId: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.checkResourceExists(
        'findAthleteByNationalId',
        'Athlete',
        nationalId,
        async () => {
          const effectiveTenantId = tenantId || await getServerTenantId();
          return TenantAwareDB.getAthleteByNationalId(nationalId, effectiveTenantId || undefined);
        },
        {
          userId,
          tenantId,
          resource: 'athlete',
        }
    );
  }

  private async checkAthleteHasActivePayments(
    athleteId: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.checkResourceExists(
        'checkAthleteHasActivePayments',
        'Athlete',
        athleteId,
        async () => {
          const effectiveTenantId = tenantId || await getServerTenantId();
          return TenantAwareDB.hasAthleteNotPaidPayment(athleteId, effectiveTenantId || undefined);
        },
        {
          userId,
          tenantId,
          resource: 'athlete',
        }
    );
  }

  /**
   * Validates bulk import athlete data
   */
  private validateBulkImportData(data: BulkImportAthleteData, locale: string = 'en'): ValidationError[] {
    const t = (key: string, options?: any) => serverTranslate(locale, key, options);
    
    const validationRules = [
      (data: BulkImportAthleteData) => validators.required('name', undefined, t)(data.name),
      (data: BulkImportAthleteData) => validators.required('surname', undefined, t)(data.surname),
      (data: BulkImportAthleteData) => validators.required('nationalId', undefined, t)(data.nationalId),
      (data: BulkImportAthleteData) => validators.nationalId('nationalId', undefined, t)(data.nationalId),
      (data: BulkImportAthleteData) => validators.required('birthDate', undefined, t)(data.birthDate),
      (data: BulkImportAthleteData) => validators.required('registrationDate', undefined, t)(data.registrationDate),
      (data: BulkImportAthleteData) => validators.required('parentName', undefined, t)(data.parentName),
      (data: BulkImportAthleteData) => validators.required('parentSurname', undefined, t)(data.parentSurname),
      (data: BulkImportAthleteData) => validators.required('parentPhone', undefined, t)(data.parentPhone),
      (data: BulkImportAthleteData) => validators.phone('parentPhone', undefined, t)(data.parentPhone),
      (data: BulkImportAthleteData) => data.parentEmail ? validators.email('parentEmail', undefined, t)(data.parentEmail) : null,
    ];

    return this.validate(data, validationRules);
  }

  /**
   * Bulk import athletes from Excel data
   */
  async bulkImportAthletes(
    athletesData: BulkImportAthleteData[],
    locale: string = 'en',
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<BulkImportResult>> {
    const effectiveTenantId = tenantId || await getServerTenantId();
    if (!effectiveTenantId) {
      throw new Error('Tenant context not found');
    }

    return this.executeOperation(
      'bulkImportAthletes',
      async () => {

        const results: BulkImportResult = {
          success: true,
          processed: 0,
          created: 0,
          errors: []
        };

        // Get payment due date offset from env (default 10 days)
        const paymentDueDaysOffset = parseInt(process.env.PAYMENT_DUE_DAYS_OFFSET || '10');

        for (let index = 0; index < athletesData.length; index++) {
          const athleteData = athletesData[index];
          const rowIndex = index + 2; // Excel row number (accounting for header)

          results.processed++;

          // Validate each athlete data
          const validationErrors = this.validateBulkImportData(athleteData, locale);
          if (validationErrors.length > 0) {
            results.errors.push({
              row: rowIndex,
              message: validationErrors.map(e => e.message).join(', ')
            });
            continue;
          }

          try {
            // Create athlete with team assignments using the action
            const createResult = await createAthleteWithTeamAssignments({
              name: athleteData.name,
              surname: athleteData.surname,
              nationalId: athleteData.nationalId,
              birthDate: athleteData.birthDate,
              registrationDate: athleteData.registrationDate,
              parentName: athleteData.parentName,
              parentSurname: athleteData.parentSurname,
              parentPhone: athleteData.parentPhone,
              parentEmail: athleteData.parentEmail || '',
              parentAddress: '',
              teamAssignments: athleteData.teamAssignments
            });

            if (!createResult.success) {
              results.errors.push({
                row: rowIndex,
                message: createResult.error || 'Failed to create athlete'
              });
              continue;
            }

            // Create payment record if balance is provided
            if (athleteData.currentBalance && parseFloat(athleteData.currentBalance) !== 0) {
              const paymentResult = await this.createPaymentForImportedAthlete(
                createResult.data!.id,
                athleteData.currentBalance,
                athleteData.currentBalanceLastPaymentDate,
                paymentDueDaysOffset,
                locale,
                userId,
                effectiveTenantId
              );

              if (!paymentResult.success) {
                logger.error('Error creating payment for imported athlete:', { error: paymentResult.error });
                // Don't fail the import for payment creation errors
              }
            }

            results.created++;
          } catch (error) {
            logger.error(`Error processing athlete at index ${index}:`, { error });
            results.errors.push({
              row: rowIndex,
              message: error instanceof Error ? error.message : 'Unknown error'
            });
          }
        }

        return results;
      },
      { userId, tenantId: effectiveTenantId }
    );
  }



  /**
   * Parses date from various formats (DD/MM/YYYY, ISO, etc.) and returns date string in YYYY-MM-DD format
   * without timezone conversion to avoid date shifting issues
   */
  private parseImportDateToString(dateValue?: Date | string): string | null {
    if (!dateValue) return null;

    let day: number, month: number, year: number;

    if (dateValue instanceof Date) {
      // Use local date components to avoid timezone issues
      day = dateValue.getDate();
      month = dateValue.getMonth() + 1; // getMonth() returns 0-indexed month
      year = dateValue.getFullYear();
    } else {
      const dateStr = dateValue.toString();

      // Try DD/MM/YYYY format first
      if (dateStr.includes('/')) {
        const parts = dateStr.split('/');
        if (parts.length === 3) {
          // Assume DD/MM/YYYY format
          day = parseInt(parts[0]);
          month = parseInt(parts[1]);
          year = parseInt(parts[2]);

          // Validate the parsed values
          if (isNaN(day) || isNaN(month) || isNaN(year) ||
              day < 1 || day > 31 || month < 1 || month > 12) {
            return null;
          }
        } else {
          return null;
        }
      } else {
        // Try standard Date parsing and extract components
        const parsedDate = new Date(dateStr);
        if (isNaN(parsedDate.getTime())) {
          return null;
        }

        day = parsedDate.getDate();
        month = parsedDate.getMonth() + 1;
        year = parsedDate.getFullYear();
      }
    }

    // Format as YYYY-MM-DD without timezone conversion
    const formattedMonth = month.toString().padStart(2, '0');
    const formattedDay = day.toString().padStart(2, '0');

    return `${year}-${formattedMonth}-${formattedDay}`;
  }

  /**
   * Gets translated payment description based on locale
   */
  private getPaymentDescription(locale: string): string {
    return getPaymentTranslation('descriptions.initialBalanceFromImport', locale);
  }

  /**
   * Creates payment record for imported athlete
   */
  async createPaymentForImportedAthlete(
    athleteId: string,
    currentBalance: string,
    balanceLastPaymentDate?: Date | string,
    paymentDueDaysOffset: number = 10,
    locale: string = 'en',
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    const t = (key: string, options?: any) => serverTranslate(locale, key, options);
    
    const validationFunctions = [
      (data: { athleteId: string; currentBalance: string }) => validators.required('athleteId', undefined, t)(data.athleteId),
      (data: { athleteId: string; currentBalance: string }) => validators.required('currentBalance', undefined, t)(data.currentBalance),
      (data: { athleteId: string; currentBalance: string }) => {
        const balance = parseFloat(data.currentBalance);
        if (isNaN(balance)) {
          return {
            field: 'currentBalance',
            message: t('common.validation.invalidNumber'),
            value: data.currentBalance,
            code: 'INVALID_FORMAT'
          };
        }
        return null;
      }
    ];

    return this.executeWithValidation(
      'createPaymentForImportedAthlete',
      { athleteId, currentBalance },
      validationFunctions,
      async (validatedData) => {
        let paymentDate: string;
        let dueDate: string;

        // Parse the balance last payment date without timezone conversion
        const parsedDateString = this.parseImportDateToString(balanceLastPaymentDate);

        if (parsedDateString) {
          // Use the exact date from Excel as both payment date and due date
          paymentDate = parsedDateString;
          dueDate = parsedDateString;
        } else {
          // No valid date provided, use current date + offset
          const today = new Date();
          const todayString = `${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, '0')}-${today.getDate().toString().padStart(2, '0')}`;

          const futureDate = new Date(today.getTime() + paymentDueDaysOffset * 24 * 60 * 60 * 1000);
          const futureDateString = `${futureDate.getFullYear()}-${(futureDate.getMonth() + 1).toString().padStart(2, '0')}-${futureDate.getDate().toString().padStart(2, '0')}`;

          paymentDate = todayString;
          dueDate = futureDateString;
        }

        await createPayment({
          athleteId: validatedData.athleteId,
          amount: Math.abs(parseFloat(validatedData.currentBalance)).toString(),
          date: paymentDate,
          dueDate,
          status: 'pending',
          type: 'fee',
          method: null, // Payment method not specified for balance adjustments
          description: this.getPaymentDescription(locale)
        });

        return null;
      },
      { userId, tenantId }
    );
  }

  /**
   * Create athlete with team assignments and balance
   * This method handles the complete athlete creation workflow including:
   * - Creating the athlete
   * - Assigning to teams
   * - Assigning payment plans
   * - Creating initial balance/prorated payments
   */
  async createAthleteWithTeamAssignmentsAndBalance(
    data: CreateAthleteData & {
      teamAssignments?: Array<{
        teamId: string;
        paymentPlanId?: string;
      }>;
      initialBalance?: string;
      useProrated?: boolean;
    },
    userId?: string,
    tenantId?: string,
    locale: string = 'en'
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'createAthleteWithTeamAssignmentsAndBalance',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        const effectiveUserId = userId || await getServerUserId();

        // 1. First create the athlete
        const athleteResult = await this.createAthlete(data, effectiveUserId?.toString(), effectiveTenantId || undefined);
        if (!athleteResult.success || !athleteResult.data?.id) {
          if(athleteResult.error?.cause instanceof BusinessRuleError) {
            throw athleteResult.error?.cause;
          }else if(athleteResult.validationErrors && athleteResult.validationErrors?.length > 0){
            throw new BusinessRuleError(
                athleteResult.validationErrors![0].message,
                'Failed to create athlete',
                undefined,
                athleteResult.error?.userMessage || 'Failed to create athlete'
            )
          }else {
            throw new BusinessRuleError(
                        'athlete_creation_failed',
                        'Failed to create athlete',
                        undefined,
                        athleteResult.error?.userMessage || 'Failed to create athlete'
                      );
          }
        }

        const athleteId = athleteResult.data.id;
        const paymentPlanAssignments: Array<{ id: string; planId: string }> = [];

        // 2. Process team assignments if provided
        if (data.teamAssignments && data.teamAssignments.length > 0) {
          const { athleteTeamService, paymentPlanAssignmentService } = await import('./index');
          
          for (const assignment of data.teamAssignments) {
            try {
              // Add athlete to team
              await athleteTeamService().addAthleteToTeam(
                athleteId,
                assignment.teamId,
                effectiveUserId?.toString(),
                effectiveTenantId || undefined
              );
              
              // If payment plan is specified, assign it
              if (assignment.paymentPlanId) {
                const paymentPlanResult = await paymentPlanAssignmentService().assignPaymentPlan({
                  athleteId,
                  planId: assignment.paymentPlanId,
                  teamId: assignment.teamId,
                  isActive: true
                }, effectiveUserId?.toString(), effectiveTenantId || undefined);
                
                // Store payment plan assignment for balance calculation
                if (paymentPlanResult.success && paymentPlanResult.data?.id) {
                  paymentPlanAssignments.push({
                    id: paymentPlanResult.data.id,
                    planId: assignment.paymentPlanId
                  });
                }
              }
            } catch (assignmentError) {
              logger.error(`Error processing team assignment for team ${assignment.teamId}:`, { error: assignmentError });
              // Continue with other assignments even if one fails
            }
          }
        }

        // 3. Handle initial balance/prorated payments
        if (data.initialBalance && parseFloat(data.initialBalance) !== 0) {
          await this.handleInitialBalance({
            athleteId,
            initialBalance: data.initialBalance,
            useProrated: data.useProrated,
            paymentPlanAssignments
          }, effectiveUserId?.toString(), effectiveTenantId || undefined, locale);
        }

        return athleteResult.data;
      },
      {
        userId,
        tenantId,
        resource: 'athlete',
        metadata: { operation: 'create_with_assignments_and_balance' },
      }
    );
  }

  /**
   * Handle initial balance creation for athletes
   * Private method to handle balance logic
   */
  private async handleInitialBalance(
    data: {
      athleteId: string;
      initialBalance: string;
      useProrated?: boolean;
      paymentPlanAssignments: Array<{ id: string; planId: string }>;
    },
    userId?: string,
    tenantId?: string,
    locale: string = 'en'
  ): Promise<void> {
    const { createPayment } = await import('../actions/payments');
    const { getPaymentPlans } = await import('../actions/payment-plans');
    const { calculateProratedAmount } = await import('../proration-utils');

    let balanceAmount = parseFloat(data.initialBalance);

    // If prorated calculation is requested and payment plans were assigned
    if (data.useProrated && data.paymentPlanAssignments.length > 0) {
      // Calculate total prorated amount from all payment plans
      const paymentPlans = await getPaymentPlans();
      let totalProratedAmount = 0;
      
      for (const assignment of data.paymentPlanAssignments) {
        const selectedPlan = paymentPlans.find(plan => plan.id === assignment.planId);
        if (selectedPlan) {
          const monthlyAmount = parseFloat(selectedPlan.monthlyValue);
          const proratedAmount = calculateProratedAmount(monthlyAmount);
          totalProratedAmount += proratedAmount;
        }
      }
      
      balanceAmount = totalProratedAmount;
    }

    // Create payment records
    if (balanceAmount > 0) {
      if (data.useProrated && data.paymentPlanAssignments.length > 0) {
        // Create separate payment records for each payment plan assignment
        const paymentPlans = await getPaymentPlans();
        
        for (const assignment of data.paymentPlanAssignments) {
          const selectedPlan = paymentPlans.find(plan => plan.id === assignment.planId);
          if (selectedPlan) {
            const monthlyAmount = parseFloat(selectedPlan.monthlyValue);
            const proratedAmount = calculateProratedAmount(monthlyAmount);
            
            if (proratedAmount > 0) {
              await createPayment({
                athleteId: data.athleteId,
                athletePaymentPlanId: assignment.id,
                amount: proratedAmount.toFixed(2),
                date: new Date().toISOString().split('T')[0],
                dueDate: new Date().toISOString().split('T')[0],
                status: 'pending',
                type: 'fee',
                method: null, // Payment method not specified for prorated payments
                description: getPaymentTranslation('descriptions.proratedBalance', locale, { planName: selectedPlan.name }),
              });
            }
          }
        }
      } else {
        // Create single payment record for manual balance
        // Check if user wanted prorated calculation for manual balance
        let finalAmount = balanceAmount;
        let descriptionKey = 'descriptions.initialBalance';
        
        if (data.useProrated) {
          // Calculate prorated amount from the manual balance
          finalAmount = calculateProratedAmount(balanceAmount);
          descriptionKey = 'descriptions.proratedBalanceGeneric';
        }
        
        await createPayment({
          athleteId: data.athleteId,
          athletePaymentPlanId: data.paymentPlanAssignments.length > 0 ? data.paymentPlanAssignments[0].id : null,
          amount: finalAmount.toFixed(2),
          date: new Date().toISOString().split('T')[0],
          dueDate: new Date().toISOString().split('T')[0],
          status: 'pending',
          type: 'fee',
          method: null, // Payment method not specified for balance adjustments
          description: getPaymentTranslation(descriptionKey, locale),
        });
      }
    }
  }
}
