"use server";

import { db } from "@/src/db";
import { athletes, payments, paymentTransactions } from "@/src/db/schema";
import { eq, and, sum, sql } from "drizzle-orm";

/**
 * Calculate athlete balance based on payments and transactions
 * Balance = Total Outstanding (Non-Completed) Payments + Positive Balance from Overpayments
 * Negative balance = athlete owes money
 * Zero balance = athlete is fully paid up
 * Positive balance = athlete has credit for future payments
 */
export async function calculateAthleteBalance(athleteId: string, tenantId: string): Promise<number> {
  // Get all payments for this athlete with their remaining amounts
  const paymentsData = await db
    .select({
      amount: payments.amount,
      paidAmount: payments.paidAmount,
      status: payments.status,
    })
    .from(payments)
    .where(
      and(
        eq(payments.athleteId, athleteId),
        eq(payments.tenantId, tenantId),
        // Include pending, overdue, and partially paid payments
        sql`${payments.status} IN ('pending', 'overdue', 'partially_paid')`
      )
    );

  // Calculate total outstanding amount (what they still owe)
  const totalOutstanding = paymentsData.reduce((sum, payment) => {
    const amount = Number(payment.amount);
    const paidAmount = Number(payment.paidAmount || 0);
    return sum + (amount - paidAmount);
  }, 0);

  // Get all transactions that are not linked to specific payments (balance adjustments, overpayments)
  const balanceTransactions = await db
    .select({
      amount: sql`CAST(${paymentTransactions.amount} AS DECIMAL(10,2))`,
      transactionType: paymentTransactions.transactionType,
    })
    .from(paymentTransactions)
    .where(
      and(
        eq(paymentTransactions.athleteId, athleteId),
        eq(paymentTransactions.tenantId, tenantId),
        sql`${paymentTransactions.paymentId} IS NULL` // Not linked to specific payment
      )
    );

  // Calculate net balance from unlinked transactions
  const netTransactionBalance = balanceTransactions.reduce((sum, transaction) => {
    const amount = Number(transaction.amount);
    switch (transaction.transactionType) {
      case 'payment':
      case 'adjustment':
        return sum + amount; // Positive balance
      case 'refund':
      case 'balance_usage':
        return sum - amount; // Reduce balance
      default:
        return sum;
    }
  }, 0);

  // Final balance = Outstanding debt (negative) + Available credit (positive)
  return netTransactionBalance - totalOutstanding;
}

/**
 * Update an athlete's balance in the database
 */
export async function updateAthleteBalance(athleteId: string, tenantId: string, userId?: bigint): Promise<number> {
  const calculatedBalance = await calculateAthleteBalance(athleteId, tenantId);
  
  await db
    .update(athletes)
    .set({
      balance: calculatedBalance.toFixed(2),
      updatedAt: new Date(),
      ...(userId && { updatedBy: userId }),
    })
    .where(
      and(
        eq(athletes.id, athleteId),
        eq(athletes.tenantId, tenantId)
      )
    );

  return calculatedBalance;
}

/**
 * Get detailed balance breakdown for an athlete
 */
export async function getAthleteBalanceBreakdown(athleteId: string, tenantId: string) {
  const paymentBreakdown = await db
    .select({
      status: payments.status,
      count: sql<number>`COUNT(*)`,
      total: sum(sql`CAST(${payments.amount} AS DECIMAL(10,2))`),
    })
    .from(payments)
    .where(
      and(
        eq(payments.athleteId, athleteId),
        eq(payments.tenantId, tenantId)
      )
    )
    .groupBy(payments.status);

  const breakdown = {
    completed: { count: 0, total: 0 },
    pending: { count: 0, total: 0 },
    overdue: { count: 0, total: 0 },
    cancelled: { count: 0, total: 0 },
  };

  paymentBreakdown.forEach(item => {
    if (item.status in breakdown) {
      breakdown[item.status as keyof typeof breakdown] = {
        count: Number(item.count),
        total: parseFloat(item.total || "0"),
      };
    }
  });

  const calculatedBalance = breakdown.pending.total + breakdown.overdue.total;
  // Return negative balance (money owed) - zero if no outstanding payments
  const finalBalance = calculatedBalance > 0 ? -calculatedBalance : 0;

  return {
    breakdown,
    calculatedBalance: finalBalance,
    totalPayments: Object.values(breakdown).reduce((sum, item) => sum + item.count, 0),
    totalAmount: Object.values(breakdown).reduce((sum, item) => sum + item.total, 0),
  };
}
