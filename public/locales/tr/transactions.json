{"transactions": {"title": "Ödeme İşlemleri", "add": "İşlem Ekle", "new": "Yeni İşlem", "edit": "İşlem Düzenle", "record": "İşlem Kaydet", "tabTransactions": "İşlemler", "search_placeholder": "İşlemleri ara...", "all_types": "<PERSON><PERSON><PERSON>", "all_methods": "<PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON>", "amount_placeholder": "<PERSON><PERSON> göre ara", "date": "<PERSON><PERSON><PERSON>", "transactionDate": "İşlem Tarihi", "referenceNumber": "<PERSON><PERSON><PERSON>", "details": {"title": "İşlem Detayları", "transactionInfo": "İşlem Bilgileri", "amount": "<PERSON><PERSON>", "transactionDate": "İşlem Tarihi", "transactionType": "İşlem Türü", "method": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "referenceNumber": "<PERSON><PERSON><PERSON>", "athlete": "<PERSON><PERSON><PERSON>", "payment": "İlgili <PERSON>", "remainingAmount": "<PERSON><PERSON>"}, "types": {"payment": "Ödeme", "balance_usage": "Bakiye Kullanımı", "refund": "İade", "adjustment": "D<PERSON>zeltme"}, "methods": {"cash": "Nakit", "bank_transfer": "<PERSON><PERSON>", "credit_card": "<PERSON><PERSON><PERSON>"}, "status": {"completed": "Tamamlandı", "pending": "Beklemede", "failed": "Başarısız"}, "messages": {"unknownAthlete": "Bilinmeyen Sporcu", "manageTransactions": "Ödeme işlemlerini yönetin ve mali faaliyetleri takip edin", "deleteSuccess": "İşlem başarıyla si<PERSON>i", "deleteError": "İşlem silinirken hata oluştu", "createSuccess": "İşlem başarıyla oluşturuldu", "createSuccessDetail": "İşlem başarıyla kaydedildi", "createError": "İşlem oluşturulurken hata oluştu", "createErrorDetail": "Lütfen tekrar deneyin veya destek ekibiyle iletişime geçin", "updateSuccess": "İşlem başarıyla güncellendi", "updateSuccessDescription": "İşlem bilgileri başarıyla güncellendi", "updateError": "İşlem güncellenirken hata oluştu", "updateErrorDetail": "Lütfen tekrar deneyin veya destek ekibiyle iletişime geçin", "processSuccess": "<PERSON><PERSON><PERSON> b<PERSON> başarıyla işlendi", "processSuccessDetail": "Ödeme mevcut bakiye kullanılarak otomatik olarak işlendi", "processError": "<PERSON><PERSON>me b<PERSON>en işlenirken hata oluş<PERSON>", "insufficientBalance": "Bu ödemeyi işlemek için yetersiz b<PERSON>ye", "balanceUsed": "Ödeme mevcut baki<PERSON>en işlendi", "partialPayment": "<PERSON><PERSON><PERSON><PERSON>me ka<PERSON>", "paymentCompleted": "Ödeme bu i<PERSON><PERSON><PERSON> ta<PERSON>landı"}, "placeholders": {"searchTransactions": "İşlemleri ara...", "enterAmount": "İşlem tutarını girin", "enterDescription": "İşlem açıklamasını girin", "enterReferenceNumber": "Referans numarasını girin (isteğe bağlı)", "selectAthlete": "<PERSON><PERSON><PERSON>", "selectPayment": "İlgili ödemeyi <PERSON>in (isteğe bağlı)", "selectMethod": "<PERSON><PERSON><PERSON>", "selectType": "İşlem türünü seçin", "searchMinChars": "Lütfen en az 3 karakter girin", "filterByType": "İşlem türüne göre filtrele", "filterByMethod": "Ödeme yöntemine göre filtrele", "filterByAthlete": "Sporcuya göre filtrele", "selectDate": "İşlem tarihini seçin"}, "actions": {"view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Sil", "process": "İşle", "record": "İşlem Kaydet", "cancel": "İptal", "save": "İşlemi <PERSON>", "saving": "Kay<PERSON>ili<PERSON>r...", "processing": "İşleniyor...", "viewPayment": "Ödemeyi <PERSON>ö<PERSON>", "viewAthlete": "Sporcuyu Görüntüle"}, "deleteDialog": {"title": "İşlemi Sil", "description": "Bu işlemi silmek istediğinizden emin misiniz? Bu işlem geri alınamaz ve işlem kaydını kalıcı olarak kaldıracak ve ödeme bakiyelerini etkileyebilir."}, "validation": {"required": "Lütfen tüm gerekli alanları doldurun", "requiredFields": "Devam etmek için lütfen tüm gerekli alanları doldurun", "invalidAmount": "Lütfen geçerli bir tutar girin", "amountRequired": "İşlem tutarı gereklidir", "athleteRequired": "Lütfen bir sporcu seçin", "methodRequired": "Lütfen bir ödeme yöntemi seçin", "typeRequired": "Lütfen bir işlem türü se<PERSON>in", "dateRequired": "İşlem tarihi gereklidir", "amountTooLarge": "İşlem tutarı ödeme tutarını aşamaz", "negativeAmount": "İşlem tutarı pozitif olmalıdır"}, "form": {"title": "İşlem Formu", "description": "<PERSON>ni bir ödeme işlemi ka<PERSON>in", "fields": {"athlete": "<PERSON><PERSON><PERSON>", "payment": "İlgili <PERSON>", "amount": "İşlem Tutarı", "transactionType": "İşlem Türü", "method": "<PERSON><PERSON><PERSON>", "transactionDate": "İşlem Tarihi", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "referenceNumber": "<PERSON><PERSON><PERSON>"}, "hints": {"amount": "Bu işlem için tutarı girin", "description": "Bu işlemle ilgili isteğe bağlı açıklama veya notlar", "referenceNumber": "İsteğe bağlı referans numarası (makbuz, banka transfer ID, vb.)", "payment": "Bu işlemin ilgili olduğu ödemeyi seçin (bakiye düzeltmeleri için isteğe bağlı)"}}, "history": {"title": "İşlem Geçmişi", "description": "Bu sporcu için tüm ödeme işlemlerini görü<PERSON><PERSON><PERSON>in", "noTransactions": "İşlem bulunamadı", "totalTransactions": "Toplam İşlem", "totalAmount": "Toplam Tutar", "lastTransaction": "Son İşlem", "timeline": "İşlem Zaman Çizelgesi"}, "summary": {"title": "İşlem Özeti", "totalPayments": "Toplam Ödemeler", "totalRefunds": "<PERSON><PERSON>", "balanceUsage": "Bakiye Kullanımı", "adjustments": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "netAmount": "Net Tutar"}}}