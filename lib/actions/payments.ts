"use server";

import { getServerTenantId, getServerUserId } from '../tenant-utils-server';
import { paymentService } from '../services';
import { updateAthleteBalance } from '../balance-calculator';
import logger from '@/lib/logger';

// Payments
export async function getPayments() {
  try {
    const tenantId = await getServerTenantId();
    const result = await paymentService().getPayments(undefined, tenantId || undefined);
    
    if (!result.success) {
      logger.error("getPayments error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to get payments");
    }
    
    return result.data;
  } catch (error) {
    logger.error("getPayments error:", { error });
    throw error;
  }
}

export async function getPaymentsPaginated(
  page: number = 1,
  limit: number = 10,
  search?: string,
  sortBy: string = 'date',
  sortOrder: 'asc' | 'desc' = 'desc',
  filters: Record<string, string> = {}
) {
  try {
    const tenantId = await getServerTenantId();
    const result = await paymentService().getPaymentsPaginated(
      { page, limit, search, sortBy, sortOrder, filters },
      undefined,
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("getPaymentsPaginated error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to get paginated payments");
    }
    
    return result.data;
  } catch (error) {
    logger.error("getPaymentsPaginated error:", { error });
    throw error;
  }
}

export async function getPaymentById(id: string) {
  try {
    const tenantId = await getServerTenantId();
    const result = await paymentService().getPaymentById(id, undefined, tenantId || undefined);
    
    if (!result.success) {
      logger.error("getPaymentById error:", { error: result.error });
      throw new Error(result.error?.userMessage || `Failed to get payment with ID ${id}`);
    }
    
    return result.data;
  } catch (error) {
    logger.error("getPaymentById error:", { error });
    throw error;
  }
}

export async function deletePayment(id: string) {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    const result = await paymentService().deletePayment(
      id,
      undefined,
      tenantId || undefined,
      userId?.toString()
    );

    if (!result.success) {
      logger.error("deletePayment error:", { error: result.error });
      throw new Error(result.error?.userMessage || `Failed to delete payment with ID ${id}`);
    }

    return result.data;
  } catch (error) {
    logger.error("deletePayment error:", { error });
    throw error;
  }
}

export async function processPayment(id: string) {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    if (!tenantId) {
      throw new Error("Tenant context not found");
    }
    
    const result = await paymentService().processPayment(
      id,
      undefined,
      userId?.toString(),
      tenantId
    );
    
    if (!result.success) {
      logger.error("processPayment error:", { error: result.error });
      throw new Error(result.error?.userMessage || `Failed to process payment with ID ${id}`);
    }

    // Update athlete balance after processing payment
    if (result.data && result.data.athleteId) {
      try {
        await updateAthleteBalance(result.data.athleteId, tenantId, userId || undefined);
      } catch (error) {
        logger.error("Error updating athlete balance after payment processing:", { error });
        // Don't throw error here as payment was processed successfully
      }
    }

    return result.data;
  } catch (error) {
    logger.error("processPayment error:", { error });
    throw error;
  }
}

export async function updatePayment(id: string, data: {
  athleteId?: string;
  athletePaymentPlanId?: string | null;
  amount?: string;
  date?: string;
  dueDate?: string;
  status?: "pending" | "completed" | "overdue" | "cancelled";
  type?: "fee" | "equipment" | "other";
  method?: "cash" | "bank_transfer" | "credit_card" | null;
  description?: string | null;
}) {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    const result = await paymentService().updatePayment(
      id,
      data,
      undefined,
      userId?.toString(),
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("updatePayment error:", { error: result.error });
      throw new Error(result.error?.userMessage || `Failed to update payment with ID ${id}`);
    }

    // Update athlete balance if status changed or amount changed
    if (result.data && result.data.athleteId && tenantId && (data.status || data.amount)) {
      try {
        await updateAthleteBalance(result.data.athleteId, tenantId, userId || undefined);
      } catch (error) {
        logger.error("Error updating athlete balance after payment update:", { error });
        // Don't throw error here as payment was updated successfully
      }
    }

    return result.data;
  } catch (error) {
    logger.error("updatePayment error:", { error });
    throw error;
  }
}

export async function createPayment(data: {
  athleteId: string;
  athletePaymentPlanId?: string | null;
  amount: string;
  date: string;
  dueDate: string;
  status: "pending" | "completed" | "overdue" | "cancelled";
  type: "fee" | "equipment" | "other";
  method: "cash" | "bank_transfer" | "credit_card" | null;
  description?: string | null;
}) {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    const result = await paymentService().createPayment(
      data,
      undefined,
      userId?.toString(),
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("createPayment error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to create payment");
    }

    // Update athlete balance after creating payment
    if (result.data && data.athleteId && tenantId) {
      try {
        await updateAthleteBalance(data.athleteId, tenantId, userId || undefined);
      } catch (error) {
        logger.error("Error updating athlete balance after payment creation:", { error });
        // Don't throw error here as payment was created successfully
      }
    }

    return result.data;
  } catch (error) {
    logger.error('createPayment error:', { error });
    throw error;
  }
}
