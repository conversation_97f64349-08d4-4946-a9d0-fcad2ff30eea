"use client";

import { ColumnDef } from "@tanstack/react-table";
import { format, isValid, parseISO } from "date-fns";
import { Payment } from "@/lib/types";
import { Button } from "@/components/ui/button";
import { BadgeStatus } from "@/components/ui/badge-status";
import Link from "next/link";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { MoreHorizontal, Pencil, Trash, CheckCircle, Eye, MessageSquare } from "lucide-react";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import { deletePayment, processPayment } from "@/lib/actions";
import { useState } from "react";
import PaymentReminderDialog from "@/components/sms/payment-reminder-dialog";

// Move cell render functions outside of the columns definition
const AmountCell = ({ payment }: { payment: Payment }) => {
  const { t } = useSafeTranslation();
  const totalAmount = parseFloat(payment.amount);
  const paidAmount = parseFloat(payment.paidAmount || '0');
  const remainingAmount = totalAmount - paidAmount;

  return (
    <div className="space-y-1">
      <div className="font-medium">
        {totalAmount.toFixed(2)} {t('common.currency')}
      </div>
      {paidAmount > 0 && (
        <div className="text-xs space-y-0.5">
          <div className="text-green-600">
            {t('payments.details.paid')}: {paidAmount.toFixed(2)} {t('common.currency')}
          </div>
          {remainingAmount > 0 && (
            <div className="text-orange-600">
              {t('payments.details.remaining')}: {remainingAmount.toFixed(2)} {t('common.currency')}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

const DateCell = ({ date, label, t }: { date: string | Date | null; label: string; t: any }) => {
  if (!date) {
    return (
      <div>
        <div className="text-xs text-muted-foreground">{label}</div>
        <div className="text-muted-foreground">{t('payments.dates.notSet')}</div>
      </div>
    );
  }

  let parsedDate: Date;
  if (typeof date === 'string') {
    parsedDate = parseISO(date);
    if (!isValid(parsedDate)) {
      return (
        <div>
          <div className="text-xs text-muted-foreground">{label}</div>
          <div className="text-muted-foreground">{t('payments.dates.invalidDate')}</div>
        </div>
      );
    }
  } else {
    parsedDate = date;
  }

  return (
    <div>
      <div className="text-xs text-muted-foreground">{label}</div>
      <div>{format(parsedDate, "dd/MM/yyyy")}</div>
    </div>
  );
};

const TypeCell = ({ type }: { type: string }) => {
  const { t } = useSafeTranslation();
  const typeKey = `payments.types.${type.toLowerCase()}`;
  return (
    <div className="capitalize">{t(typeKey)}</div>
  );
};



const DescriptionCell = ({ description }: { description: string }) => {
  const { t } = useSafeTranslation();
  
  if (!description) return <div>-</div>;
  
    // Check if the description is a translation key
    if (description.startsWith('descriptions.')) {
      const translationKey = `payments.${description}`;
      
      // Handle different types of description keys
      if (description === 'descriptions.initialBalance') {
        return <div>{t('payments.descriptions.initialBalance')}</div>;
      }
      
      if (description === 'descriptions.initialBalanceFromImport') {
        return <div>{t('payments.descriptions.initialBalanceFromImport')}</div>;
      }
      
      if (description === 'descriptions.proratedBalanceGeneric') {
        return <div>{t('payments.descriptions.proratedBalanceGeneric')}</div>;
      }
      
      if (description.startsWith('descriptions.proratedBalance')) {
        // For prorated balance, if we only have the key without plan name,
        // use a simpler description that doesn't require plan name interpolation
        return <div>{t('payments.descriptions.proratedBalanceGeneric')}</div>;
      }
      
      // Fallback: try to translate the key directly
      const translated = t(translationKey);
      if (translated !== translationKey) {
        return <div>{translated}</div>;
      }
    }  // Check for other common patterns and handle them
  if (description.includes('remaining days of the month')) {
    // This might be an already-interpolated prorated balance description
    // Try to detect the plan name pattern
    const match = description.match(/Prorated balance for (.+) \(remaining days of the month\)/);
    if (match) {
      const planName = match[1];
      return <div>{t('payments.descriptions.proratedBalance', { planName })}</div>;
    }
  }
  
  if (description === 'Initial balance') {
    return <div>{t('payments.descriptions.initialBalance')}</div>;
  }
  
  if (description === 'Initial balance from import') {
    return <div>{t('payments.descriptions.initialBalanceFromImport')}</div>;
  }
  
  // Return the original description if no translation pattern matches
  return <div>{description}</div>;
};

const AthleteCell = ({ payment }: { payment: Payment }) => {
  const { t } = useSafeTranslation();
  
  if (!payment.athlete) {
    return <div className="text-muted-foreground">{t('payments.messages.unknownAthlete')}</div>;
  }
  
  return (
    <div>
      <div className="font-medium">{payment.athlete.name} {payment.athlete.surname}</div>
      {payment.athlete.parentEmail && (
        <div className="text-xs text-muted-foreground">
          {payment.athlete.parentEmail}
        </div>
      )}
    </div>
  );
};

const ActionsCell = ({ payment }: { payment: Payment }) => {
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);
  
  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await deletePayment(payment.id);
      toast({
        title: t('payments.messages.deleteSuccess'),
        variant: "default",
      });
      router.refresh();
    } catch (error) {
      toast({
        title: t('payments.messages.deleteError'),
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const handleProcess = async () => {
    try {
      await processPayment(payment.id);
      toast({
        title: t('payments.messages.processSuccess'),
        description: t('payments.messages.processSuccessDetail'),
        variant: "default",
      });
      router.refresh();
    } catch (error) {
      toast({
        title: t('payments.messages.processError'),
        variant: "destructive",
      });
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">{t('payments.actions.openMenu')}</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>{t('common.actionsHeader')}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link href={`/payments/${payment.id}`} className="flex items-center">
            <Eye className="mr-2 h-4 w-4" />
            {t('payments.actions.viewDetails')}
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={`/payments/${payment.id}/edit`} className="flex items-center">
            <Pencil className="mr-2 h-4 w-4" />
            {t('payments.actions.edit')}
          </Link>
        </DropdownMenuItem>
        {payment.status !== "completed" && (
          <DropdownMenuItem onClick={handleProcess}>
            <CheckCircle className="mr-2 h-4 w-4" />
            {t('payments.actions.process')}
          </DropdownMenuItem>
        )}
        {(payment.status === "pending" || payment.status === "overdue") && (
          <PaymentReminderDialog
            payments={[payment]}
            trigger={
              <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                <MessageSquare className="mr-2 h-4 w-4" />
                {t('sms:actions.sendSmsReminder')}
              </DropdownMenuItem>
            }
          />
        )}
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <DropdownMenuItem
              className="text-destructive focus:text-destructive"
              onSelect={(e) => e.preventDefault()}
            >
              <Trash className="mr-2 h-4 w-4" />
              {t('payments.actions.delete')}
            </DropdownMenuItem>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                {t('payments.deleteDialog.title')}
              </AlertDialogTitle>
              <AlertDialogDescription>
                {t('payments.deleteDialog.description')}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={isDeleting}>
                {t('common.actions.cancel')}
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                disabled={isDeleting}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {isDeleting 
                  ? t('common.actions.deleting') 
                  : t('common.actions.delete')
                }
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export const createPaymentColumns = (t: any): ColumnDef<Payment>[] => [
  {
    accessorKey: "athleteId",
    header: () => t('payments.details.athlete'),
    cell: ({ row }) => <AthleteCell payment={row.original} />,
  },
  {
    accessorKey: "amount",
    header: () => t('payments.details.amount'),
    cell: ({ row }) => <AmountCell payment={row.original} />,
  },
  {
    accessorKey: "date",
    header: () => t('payments.details.date'),
    cell: ({ row }) => (
      <div className="space-y-1">
        <DateCell date={row.original.date} label={t('payments.dates.billing')} t={t} />
        <DateCell date={row.original.dueDate} label={t('payments.dates.due')} t={t} />
      </div>
    ),
  },
  {
    accessorKey: "type",
    header: () => t('payments.details.type'),
    cell: ({ row }) => <TypeCell type={row.original.type} />,
  },

  {
    accessorKey: "status",
    header: () => t('payments.details.status'),
    cell: ({ row }) => <BadgeStatus status={row.original.status} type="payment" />,
  },
  {
    accessorKey: "description",
    header: () => t('payments.details.description'),
    cell: ({ row }) => <DescriptionCell description={row.getValue("description")} />,
  },
  {
    id: "actions",
    header: () => t('common.actionsHeader'),
    cell: ({ row }) => <ActionsCell payment={row.original} />,
  },
];