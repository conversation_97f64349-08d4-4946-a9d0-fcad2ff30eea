{"transactions": {"title": "Payment Transactions", "add": "Add Transaction", "new": "New Transaction", "edit": "Edit Transaction", "record": "Record Transaction", "tabTransactions": "Transactions", "search_placeholder": "Search transactions...", "all_types": "All Types", "all_methods": "All Methods", "amount": "Amount", "amount_placeholder": "Search by amount", "date": "Date", "transactionDate": "Transaction Date", "referenceNumber": "Reference Number", "details": {"title": "Transaction Details", "transactionInfo": "Transaction Information", "amount": "Amount", "transactionDate": "Transaction Date", "transactionType": "Transaction Type", "method": "Payment Method", "description": "Description", "referenceNumber": "Reference Number", "athlete": "Athlete", "payment": "Related Payment", "remainingAmount": "Remaining Amount"}, "types": {"payment": "Payment", "balance_usage": "Balance Usage", "refund": "Refund", "adjustment": "Adjustment"}, "methods": {"cash": "Cash", "bank_transfer": "Bank Transfer", "credit_card": "Credit Card"}, "status": {"completed": "Completed", "pending": "Pending", "failed": "Failed"}, "messages": {"unknownAthlete": "Unknown Athlete", "manageTransactions": "Manage payment transactions and track financial activities", "deleteSuccess": "Transaction deleted successfully", "deleteError": "Failed to delete transaction", "createSuccess": "Transaction created successfully", "createSuccessDetail": "Transaction has been recorded successfully", "createError": "Failed to create transaction", "createErrorDetail": "Please try again or contact support", "updateSuccess": "Transaction updated successfully", "updateSuccessDescription": "Transaction information has been updated successfully", "updateError": "Failed to update transaction", "updateErrorDetail": "Please try again or contact support", "processSuccess": "Payment processed from balance successfully", "processSuccessDetail": "Payment has been automatically processed using available balance", "processError": "Failed to process payment from balance", "insufficientBalance": "Insufficient balance to process this payment", "balanceUsed": "Payment processed from existing balance", "partialPayment": "Partial payment recorded", "paymentCompleted": "Payment completed with this transaction"}, "placeholders": {"searchTransactions": "Search transactions...", "enterAmount": "Enter transaction amount", "enterDescription": "Enter transaction description", "enterReferenceNumber": "Enter reference number (optional)", "selectAthlete": "Select athlete", "selectPayment": "Select related payment (optional)", "selectMethod": "Select payment method", "selectType": "Select transaction type", "searchMinChars": "Please enter at least 3 characters", "filterByType": "Filter by transaction type", "filterByMethod": "Filter by payment method", "filterByAthlete": "Filter by athlete", "selectDate": "Select transaction date"}, "actions": {"view": "View", "edit": "Edit", "delete": "Delete", "process": "Process", "record": "Record Transaction", "cancel": "Cancel", "save": "Save Transaction", "saving": "Saving...", "processing": "Processing...", "viewPayment": "View Payment", "viewAthlete": "View Athlete"}, "deleteDialog": {"title": "Delete Transaction", "description": "Are you sure you want to delete this transaction? This action cannot be undone and will permanently remove the transaction record and may affect payment balances."}, "validation": {"required": "Please fill in all required fields", "requiredFields": "Please fill in all required fields to continue", "invalidAmount": "Please enter a valid amount", "amountRequired": "Transaction amount is required", "athleteRequired": "Please select an athlete", "methodRequired": "Please select a payment method", "typeRequired": "Please select a transaction type", "dateRequired": "Transaction date is required", "amountTooLarge": "Transaction amount cannot exceed the payment amount", "negativeAmount": "Transaction amount must be positive"}, "form": {"title": "Transaction Form", "description": "Record a new payment transaction", "fields": {"athlete": "Athlete", "payment": "Related Payment", "amount": "Transaction Amount", "transactionType": "Transaction Type", "method": "Payment Method", "transactionDate": "Transaction Date", "description": "Description", "referenceNumber": "Reference Number"}, "hints": {"amount": "Enter the amount for this transaction", "description": "Optional description or notes about this transaction", "referenceNumber": "Optional reference number (receipt, bank transfer ID, etc.)", "payment": "Select the payment this transaction is related to (optional for balance adjustments)"}}, "history": {"title": "Transaction History", "description": "View all payment transactions for this athlete", "noTransactions": "No transactions found", "totalTransactions": "Total Transactions", "totalAmount": "Total Amount", "lastTransaction": "Last Transaction", "timeline": "Transaction Timeline"}, "summary": {"title": "Transaction Summary", "totalPayments": "Total Payments", "totalRefunds": "Total Refunds", "balanceUsage": "Balance Usage", "adjustments": "Adjustments", "netAmount": "Net Amount"}}}