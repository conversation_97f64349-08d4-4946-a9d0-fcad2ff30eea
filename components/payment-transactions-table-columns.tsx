"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Eye, Edit, Trash2, CreditCard, User } from "lucide-react";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { PaymentTransaction } from "@/lib/types";
import Link from "next/link";
import { format } from "date-fns";
import { tr, enUS } from "date-fns/locale";

const AthleteCell = ({ transaction }: { transaction: PaymentTransaction }) => {
  const { t } = useSafeTranslation();
  
  if (!transaction.athlete) {
    return <div className="text-muted-foreground italic">{t('transactions.messages.unknownAthlete')}</div>;
  }

  return (
    <div className="space-y-1">
      <div className="font-medium">
        {transaction.athlete.name} {transaction.athlete.surname}
      </div>
      {transaction.athlete.parentName && (
        <div className="text-sm text-muted-foreground">
          {transaction.athlete.parentName}
        </div>
      )}
    </div>
  );
};

const AmountCell = ({ amount }: { amount: string }) => {
  const { t } = useSafeTranslation();
  return (
    <div className="font-medium">
      {parseFloat(amount).toFixed(2)} {t('common.currency')}
    </div>
  );
};

const DateCell = ({ date }: { date: Date }) => {
  const { t } = useSafeTranslation();
  const locale = t('common.locale') === 'tr-TR' ? tr : enUS;
  
  return (
    <div className="text-sm">
      {format(new Date(date), 'dd MMM yyyy HH:mm', { locale })}
    </div>
  );
};

const TypeCell = ({ type }: { type: string }) => {
  const { t } = useSafeTranslation();
  const typeKey = `transactions.types.${type.toLowerCase()}`;
  
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'payment':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'balance_usage':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'refund':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      case 'adjustment':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  return (
    <Badge className={getTypeColor(type)}>
      {t(typeKey)}
    </Badge>
  );
};

const MethodCell = ({ method }: { method: string }) => {
  const { t } = useSafeTranslation();
  const methodKey = `transactions.methods.${method.toLowerCase()}`;
  return (
    <div className="capitalize">{t(methodKey)}</div>
  );
};

const PaymentCell = ({ transaction }: { transaction: PaymentTransaction }) => {
  const { t } = useSafeTranslation();
  
  if (!transaction.payment) {
    return <div className="text-muted-foreground italic">-</div>;
  }

  return (
    <div className="space-y-1">
      <div className="text-sm font-medium">
        {parseFloat(transaction.payment.amount).toFixed(2)} {t('common.currency')}
      </div>
      <div className="text-xs text-muted-foreground">
        {t(`payments.types.${transaction.payment.type}`)}
      </div>
    </div>
  );
};

const DescriptionCell = ({ description }: { description: string | null | undefined }) => {
  if (!description) return <div className="text-muted-foreground">-</div>;

  return (
    <div className="max-w-[200px] truncate" title={description}>
      {description}
    </div>
  );
};

const ActionsCell = ({ transaction }: { transaction: PaymentTransaction }) => {
  const { t } = useSafeTranslation();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">{t('common.actionsHeader')}</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>{t('common.actionsHeader')}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {/* View Athlete */}
        <DropdownMenuItem asChild>
          <Link href={`/athletes/${transaction.athleteId}`}>
            <User className="mr-2 h-4 w-4" />
            {t('transactions.actions.viewAthlete')}
          </Link>
        </DropdownMenuItem>
        
        {/* View Payment */}
        {transaction.paymentId && (
          <DropdownMenuItem asChild>
            <Link href={`/payments/${transaction.paymentId}`}>
              <CreditCard className="mr-2 h-4 w-4" />
              {t('transactions.actions.viewPayment')}
            </Link>
          </DropdownMenuItem>
        )}
        
        <DropdownMenuSeparator />
        
        {/* View Transaction Details */}
        <DropdownMenuItem asChild>
          <Link href={`/payments/transactions/${transaction.id}`}>
            <Eye className="mr-2 h-4 w-4" />
            {t('transactions.actions.view')}
          </Link>
        </DropdownMenuItem>
        
        {/* Edit Transaction */}
        <DropdownMenuItem asChild>
          <Link href={`/payments/transactions/${transaction.id}/edit`}>
            <Edit className="mr-2 h-4 w-4" />
            {t('transactions.actions.edit')}
          </Link>
        </DropdownMenuItem>
        
        {/* Delete Transaction */}
        <DropdownMenuItem className="text-red-600">
          <Trash2 className="mr-2 h-4 w-4" />
          {t('transactions.actions.delete')}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export const createPaymentTransactionColumns = (t: any): ColumnDef<PaymentTransaction>[] => [
  {
    accessorKey: "athleteId",
    header: () => t('transactions.details.athlete'),
    cell: ({ row }) => <AthleteCell transaction={row.original} />,
  },
  {
    accessorKey: "amount",
    header: () => t('transactions.details.amount'),
    cell: ({ row }) => <AmountCell amount={row.original.amount} />,
  },
  {
    accessorKey: "transactionDate",
    header: () => t('transactions.details.transactionDate'),
    cell: ({ row }) => <DateCell date={row.original.transactionDate} />,
  },
  {
    accessorKey: "transactionType",
    header: () => t('transactions.details.transactionType'),
    cell: ({ row }) => <TypeCell type={row.original.transactionType} />,
  },
  {
    accessorKey: "method",
    header: () => t('transactions.details.method'),
    cell: ({ row }) => <MethodCell method={row.original.method} />,
  },
  {
    accessorKey: "paymentId",
    header: () => t('transactions.details.payment'),
    cell: ({ row }) => <PaymentCell transaction={row.original} />,
  },
  {
    accessorKey: "description",
    header: () => t('transactions.details.description'),
    cell: ({ row }) => <DescriptionCell description={row.original.description} />,
  },
  {
    accessorKey: "referenceNumber",
    header: () => t('transactions.details.referenceNumber'),
    cell: ({ row }) => (
      <div className="text-sm font-mono">
        {row.original.referenceNumber || '-'}
      </div>
    ),
  },
  {
    id: "actions",
    header: () => t('common.actionsHeader'),
    cell: ({ row }) => <ActionsCell transaction={row.original} />,
  },
];
