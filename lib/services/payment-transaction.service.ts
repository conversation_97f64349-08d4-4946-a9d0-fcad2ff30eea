import { BaseService } from './base';
import { TenantAwareDB } from '../db';
import { ServiceResult } from '../errors/types';
import { updateAthleteBalance } from '../balance-calculator';

export class PaymentTransactionService extends BaseService {
  constructor() {
    super('PaymentTransactionService');
  }

  async getPaymentTransactions(
    tenantId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeDatabaseOperation(
      'getPaymentTransactions',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        return await TenantAwareDB.getPaymentTransactions(resolvedTenantId);
      },
      { tenantId: effectiveTenantId || tenantId }
    );
  }

  async getPaymentTransactionsPaginated(
    page: number = 1,
    limit: number = 10,
    search?: string,
    sortBy: string = 'transactionDate',
    sortOrder: 'asc' | 'desc' = 'desc',
    filters: Record<string, string> = {},
    tenantId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<{ data: any[]; pagination: any }>> {
    return this.executeDatabaseOperation(
      'getPaymentTransactionsPaginated',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        return await TenantAwareDB.getPaymentTransactionsPaginated(
          page,
          limit,
          search,
          sortBy,
          sortOrder,
          filters,
          resolvedTenantId
        );
      },
      { tenantId: effectiveTenantId || tenantId }
    );
  }

  async getTransactionsByPaymentId(
    paymentId: string,
    tenantId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeDatabaseOperation(
      'getTransactionsByPaymentId',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        return await TenantAwareDB.getTransactionsByPaymentId(paymentId, resolvedTenantId);
      },
      { tenantId: effectiveTenantId || tenantId }
    );
  }

  async getTransactionsByAthleteId(
    athleteId: string,
    tenantId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeDatabaseOperation(
      'getTransactionsByAthleteId',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        return await TenantAwareDB.getTransactionsByAthleteId(athleteId, resolvedTenantId);
      },
      { tenantId: effectiveTenantId || tenantId }
    );
  }

  async createPaymentTransaction(
    data: {
      paymentId?: string | null;
      athleteId: string;
      amount: string;
      transactionType: 'payment' | 'balance_usage' | 'refund' | 'adjustment';
      method: 'cash' | 'bank_transfer' | 'credit_card';
      description?: string | null;
      referenceNumber?: string | null;
    },
    tenantId?: string,
    userId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeDatabaseOperation(
      'createPaymentTransaction',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        const userIdBigInt = userId ? BigInt(userId) : undefined;
        
        // Create the transaction
        const transaction = await TenantAwareDB.createPaymentTransaction(
          data,
          resolvedTenantId,
          userIdBigInt
        );

        // If this is a payment transaction, update the payment's paid amount
        if (data.paymentId && data.transactionType === 'payment') {
          await this.updatePaymentPaidAmount(data.paymentId, resolvedTenantId!, userIdBigInt);
        }

        // Update athlete balance
        await updateAthleteBalance(data.athleteId, resolvedTenantId!, userIdBigInt);

        return transaction;
      },
      { tenantId: effectiveTenantId || tenantId, userId }
    );
  }

  async processPaymentFromBalance(
    paymentId: string,
    amount: string,
    tenantId?: string,
    userId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeDatabaseOperation(
      'processPaymentFromBalance',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        const userIdBigInt = userId ? BigInt(userId) : undefined;

        // Get payment details
        const payment = await TenantAwareDB.getPaymentById(paymentId, resolvedTenantId);
        if (!payment) {
          throw new Error('Payment not found');
        }

        // Create balance usage transaction
        await TenantAwareDB.createPaymentTransaction(
          {
            paymentId,
            athleteId: payment.athleteId,
            amount,
            transactionType: 'balance_usage',
            method: 'cash', // Default method for balance usage
            description: 'Payment processed from existing balance',
          },
          resolvedTenantId,
          userIdBigInt
        );

        // Update payment's paid amount
        await this.updatePaymentPaidAmount(paymentId, resolvedTenantId!, userIdBigInt);

        // Update athlete balance
        await updateAthleteBalance(payment.athleteId, resolvedTenantId!, userIdBigInt);

        return true;
      },
      { tenantId: effectiveTenantId || tenantId, userId }
    );
  }

  private async updatePaymentPaidAmount(
    paymentId: string,
    tenantId: string,
    userId?: bigint
  ): Promise<void> {
    // Get all payment transactions for this payment
    const transactions = await TenantAwareDB.getTransactionsByPaymentId(paymentId, tenantId);
    
    // Calculate total paid amount from transactions
    const totalPaid = transactions
      .filter(t => t.transactionType === 'payment' || t.transactionType === 'balance_usage')
      .reduce((sum, t) => sum + parseFloat(t.amount), 0);

    // Get payment details to check total amount
    const payment = await TenantAwareDB.getPaymentById(paymentId, tenantId);
    if (!payment) return;

    const totalAmount = parseFloat(payment.amount);
    const paidAmount = Math.min(totalPaid, totalAmount);
    
    // Determine new status
    let newStatus: 'pending' | 'partially_paid' | 'completed' = 'pending';
    if (paidAmount >= totalAmount) {
      newStatus = 'completed';
    } else if (paidAmount > 0) {
      newStatus = 'partially_paid';
    }

    // Update payment
    await TenantAwareDB.updatePayment(
      paymentId,
      {
        paidAmount: paidAmount.toFixed(2),
        status: newStatus,
      },
      tenantId,
      userId
    );
  }

  async deletePaymentTransaction(
    id: string,
    tenantId?: string,
    userId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeDatabaseOperation(
      'deletePaymentTransaction',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        const userIdBigInt = userId ? BigInt(userId) : undefined;

        // Get transaction details before deletion
        const transaction = await TenantAwareDB.getPaymentTransactionById(id, resolvedTenantId);
        if (!transaction) {
          throw new Error('Transaction not found');
        }

        // Delete the transaction
        await TenantAwareDB.deletePaymentTransaction(id, resolvedTenantId);

        // Update payment's paid amount if this was linked to a payment
        if (transaction.paymentId) {
          await this.updatePaymentPaidAmount(transaction.paymentId, resolvedTenantId!, userIdBigInt);
        }

        // Update athlete balance
        await updateAthleteBalance(transaction.athleteId, resolvedTenantId!, userIdBigInt);

        return true;
      },
      { tenantId: effectiveTenantId || tenantId, userId }
    );
  }
}
