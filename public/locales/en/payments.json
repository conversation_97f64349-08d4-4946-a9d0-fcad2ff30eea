{"payments": {"title": "Payments", "add": "Add Payment", "new": "New Payment", "edit": "Edit Payment", "recordPayment": "Record Payment", "selectType": "Select payment type", "selectStatus": "Select payment status", "selectMethod": "Select payment method", "newPaymentPlan": "New Payment Plan", "tabPayments": "Payments", "tabPlans": "Payment Plans", "createPlan": "Create a new payment plan", "search_placeholder": "Search payments...", "athlete_name": "Athlete Name", "athlete_surname": "Athlete Surname", "athlete_name_placeholder": "Search by athlete name", "athlete_surname_placeholder": "Search by athlete surname", "all_statuses": "All Statuses", "all_types": "All Types", "amount": "Amount", "amount_placeholder": "Search by amount", "date": "Date", "dueDate": "Due Date", "status_pending": "Pending", "status_completed": "Completed", "status_overdue": "Overdue", "status_cancelled": "Cancelled", "type_fee": "Fee", "type_equipment": "Equipment", "type_other": "Other", "type": {"fee": "Fee", "equipment": "Equipment", "other": "Other"}, "typeLabel": "Type", "athleteId": "Athlete ID", "fromDate": "From Date", "toDate": "To Date", "filters": {"selectFromDate": "Select from date", "selectToDate": "Select to date"}, "details": {"title": "Payment Details", "paymentInfo": "Payment Information", "amount": "Amount", "date": "Date", "dueDate": "Due Date", "status": "Status", "type": "Type", "method": "Payment Method", "description": "Description", "originalAmount": "Original Amount", "originalDate": "Original Date", "originalStatus": "Original Status", "athlete": "Athlete", "frequency": "Frequency", "paid": "Paid", "remaining": "Remaining"}, "dates": {"notSet": "Not set", "invalidDate": "Invalid date", "due": "Due Date", "billing": "Billing Date"}, "status": {"completed": "Completed", "pending": "Pending", "overdue": "Overdue", "cancelled": "Cancelled", "partially_paid": "Partially Paid"}, "types": {"fee": "Fee", "equipment": "Equipment", "other": "Other"}, "methods": {"cash": "Cash", "bank_transfer": "Bank Transfer", "credit_card": "Credit Card", "not_specified": "Not specified"}, "methodHint": "Payment method can be specified when the payment is completed", "installment": "Installment", "frequency": {"monthly": "Monthly", "quarterly": "Quarterly", "annually": "Annually"}, "actions": {"view": "View", "edit": "Edit", "delete": "Delete", "process": "Process Payment", "viewDetails": "View details", "openMenu": "Open menu", "deleteConfirm": "Are you sure you want to delete this payment?", "deletePlanConfirm": "Are you sure you want to delete this payment plan?", "generateReceipt": "Generate Receipt", "printReceipt": "Print Receipt"}, "descriptions": {"initialBalance": "Initial balance", "initialBalanceFromImport": "Initial balance from import", "proratedBalance": "Prorated balance for {{planName}} (remaining days of the month)", "proratedBalanceGeneric": "Prorated balance (remaining days of the month)", "remainingDaysOfMonth": "remaining days of the month"}, "deleteDialog": {"title": "Delete Payment", "description": "Are you sure you want to delete this payment? This action cannot be undone and will permanently remove the payment record."}, "messages": {"unknownAthlete": "Unknown Athlete", "managePayments": "Manage payments and track financial transactions", "deleteSuccess": "Payment deleted successfully", "deleteError": "Failed to delete payment", "createSuccess": "Payment created successfully", "createSuccessDetail": "Payment has been created successfully", "createError": "Failed to create payment", "createErrorDetail": "Please try again or contact support", "updateSuccess": "Payment updated successfully", "updateSuccessDescription": "Payment information has been updated successfully", "updateError": "Failed to update payment", "updateErrorDetail": "Please try again or contact support", "updateSuccessDetail": "Payment information has been saved successfully", "processSuccess": "Payment processed successfully", "processSuccessDetail": "The payment status has been updated to 'Completed'", "processError": "Failed to process payment"}, "placeholders": {"searchPayments": "Search payments...", "enterAmount": "Enter amount", "enterDescription": "Enter description", "selectAthlete": "Select athlete", "searchMinChars": "Please enter at least 3 characters", "filterByStatus": "Filter by status", "filterByType": "Filter by type", "filterByAthlete": "Filter by athlete ID", "selectDate": "Select invoice date", "selectDueDate": "Select due date"}, "paymentPlan": "Payment Plan", "statusLabel": "Status", "plans": {"name": "Plan Name", "amount": "Amount", "description": "Description", "monthlyValue": "Monthly Value", "assignDay": "Assign Day", "dueDay": "Due Day", "dayOfMonth": "{{day}}th of each month", "plan": "Payment Plan", "select": "Select payment plan", "noAvailable": "No payment plans available", "assignments": {"title": "Payment Plan Assignments", "assign": "Assign", "assignNew": "Assign New Plan", "assignFirst": "Assign First Plan", "assignToTeam": "Assign Payment Plan to Team", "selectRequired": "Please select both team and payment plan", "success": "Payment plan assigned successfully", "assignSuccess": "Payment plan assigned successfully", "removeSuccess": "Payment plan removed successfully", "alreadyAssigned": "This payment plan is already assigned to this team", "error": "Failed to assign payment plan", "deactivated": "Payment plan deactivated successfully", "activated": "Payment plan activated successfully", "deleted": "Payment plan assignment deleted successfully", "deactivate": "Deactivate payment plan", "activate": "Activate payment plan", "delete": "Delete assignment", "oneActivePerTeam": "Only one active payment plan per team is allowed", "noAssignments": "No payment plan assignments yet", "assignedDate": "Assigned Date"}}, "viewPlan": "View Plan Details", "receipt": {"title": "Payment Receipt", "number": "Receipt Number", "to": "Receipt To", "for": "Payment For", "issueDate": "Issue Date"}, "editPage": {"title": "Edit Payment", "details": "Payment Details", "description": "Update payment information and settings", "currentInfo": "Current Information", "descriptionPlaceholder": "Add payment description or notes...", "completedWarning": "This payment is marked as completed. Changes may affect financial records."}, "validation": {"required": "Please fill in all required fields", "requiredFields": "Please fill in all required fields to continue", "dueDateBeforeDate": "Due date cannot be before payment date"}, "addPage": {"title": "Add New Payment", "description": "Create a new payment record for an athlete", "details": "Payment Details", "formDescription": "Fill in the payment information below", "descriptionPlaceholder": "Add payment description or notes...", "info": "Payment Information", "manualPaymentNote": "This is a manual payment record. No automatic processing will occur.", "paymentPlanNote": "For recurring payments, consider setting up a payment plan instead."}, "planDetail": {"subtitle": "Payment Plan • Created {{date}}", "editPlan": "Edit Plan", "status": {"active": "Active"}, "actions": {"delete": "Delete Plan"}, "deleteDialog": {"title": "Delete Payment Plan", "description": "Are you sure you want to delete this payment plan? This action cannot be undone.", "descriptionWithAssignments": "This payment plan has {{count}} assigned athlete(s). Deleting this plan will remove all assignments. Are you sure you want to continue?"}, "messages": {"deleteSuccess": "Payment plan deleted successfully", "deleteError": "Failed to delete payment plan"}, "overview": {"title": "Plan Overview", "paymentAmount": "Monthly Value", "billedFrequency": "Recurring {{frequency}} payment", "description": "Description"}, "details": {"status": "Status"}, "stats": {"title": "Quick Stats", "assignDay": "Assign Day", "dueDay": "Due Day", "gracePeriod": "<PERSON> Period", "branches": "Branches"}, "schedule": {"title": "Payment Schedule", "assignedOn": "Assigned on {{day}}", "dueOn": "Due on {{day}}", "monthlyInterval": "day every month", "ordinalSt": "st", "ordinalNd": "nd", "ordinalRd": "rd", "ordinalTh": "th", "paymentCycle": "Payment cycle: {{assignOrdinal}} to {{dueOrdinal}} of each month", "gracePeriod": "{{days}} day grace period"}, "branches": {"title": "Available Branches", "description": "This plan is available at {{count}} branch", "description_other": "This plan is available at {{count}} branches", "noBranches": "No branches assigned to this plan."}}, "planEdit": {"title": "Edit Payment Plan", "subtitle": "Update the payment plan details and settings", "branchesCount_one": "{{count}} branch", "branchesCount_other": "{{count}} branches", "basicInfo": {"title": "Basic Information", "subtitle": "Update the plan name, amount, and billing frequency"}, "branches": {"title": "Available Branches", "subtitle": "Select which branches can offer this payment plan", "loading": "Loading branches...", "selected": "Selected: {{selected}} of {{total}} branches", "selectAtLeastOne": "Please select at least one branch for this payment plan."}, "fields": {"name": "Plan Name", "description": "Description", "monthlyValue": "Monthly Value", "assignDay": "Assign Day", "dueDay": "Due Day", "status": "Status"}, "placeholders": {"description": "Optional description of the payment plan", "selectStatus": "Select status"}, "frequency": {"monthly": "Monthly"}, "status": {"active": "Active", "inactive": "Inactive"}, "summary": {"title": "Plan Preview", "monthlyValue": "Monthly Value", "assignDay": "Assign Day", "dueDay": "Due Day", "branches": "Branches", "status": "Status"}, "buttons": {"save": "Save Changes", "saving": "Saving..."}, "messages": {"updateSuccess": "Payment plan updated successfully"}, "errors": {"updateFailed": "Failed to update payment plan", "failedToLoadBranches": "Failed to load branches"}}, "planCreate": {"title": "Create Payment Plan", "subtitle": "Set up a new payment plan for your organization", "basicInfo": {"title": "Basic Information", "subtitle": "Enter the basic details for this payment plan"}, "fields": {"name": "Plan Name", "amount": "Amount (TL)", "description": "Description", "assignDay": "Assign Day", "dueDay": "Due Day", "status": "Status"}, "placeholders": {"name": "Enter plan name", "amount": "0.00", "description": "Optional description of the payment plan", "selectStatus": "Select status"}, "branches": {"title": "Branch Selection", "subtitle": "Choose which branches can offer this payment plan", "selectAtLeastOne": "Please select at least one branch", "loading": "Loading branches...", "selected": "Selected: {{selected}} of {{total}} branches"}, "summary": {"title": "Plan Preview", "amount": "Amount", "assignDay": "Assign Day", "dueDay": "Due Day", "status": "Status", "branches": "Branches"}, "buttons": {"save": "Create Plan", "saving": "Creating...", "cancel": "Cancel"}, "messages": {"createSuccess": "Payment plan created successfully", "createError": "Failed to create payment plan", "validationError": "Please check the form for errors"}, "validation": {"branchesRequired": "At least one branch must be selected", "invalidDays": "Assign day and due day must be between 1 and 31", "nameRequired": "Plan name is required", "nameLength": "Plan name must be between 3 and 100 characters", "monthlyValueRequired": "Monthly value is required", "monthlyValuePositive": "Monthly value must be a positive number", "monthlyValueMax": "Monthly value cannot exceed 100,000 TL", "assignDayRange": "Assign day must be between 1 and 31", "dueDayRange": "Due day must be between 1 and 31", "invalidStatus": "Status must be either active or inactive", "assignBeforeDue": "Assign day must be before due day"}}, "assignments": {"overview": {"error": "Failed to assign payment plan"}}}}