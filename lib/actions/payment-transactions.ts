"use server";

import { getServerTenantId, getServerUserId } from '../tenant-utils-server';
import { paymentTransactionService } from '../services';
import logger from '@/lib/logger';

// Payment Transactions
export async function getPaymentTransactions() {
  try {
    const tenantId = await getServerTenantId();
    const result = await paymentTransactionService().getPaymentTransactions(undefined, tenantId || undefined);
    
    if (!result.success) {
      logger.error("getPaymentTransactions error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to get payment transactions");
    }
    
    return result.data;
  } catch (error) {
    logger.error("getPaymentTransactions error:", { error });
    throw error;
  }
}

export async function getPaymentTransactionsPaginated(
  page: number = 1,
  limit: number = 10,
  search?: string,
  sortBy: string = 'transactionDate',
  sortOrder: 'asc' | 'desc' = 'desc',
  filters: Record<string, string> = {}
) {
  try {
    const tenantId = await getServerTenantId();
    const result = await paymentTransactionService().getPaymentTransactionsPaginated(
      page,
      limit,
      search,
      sortBy,
      sortOrder,
      filters,
      undefined,
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("getPaymentTransactionsPaginated error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to get paginated payment transactions");
    }
    
    return result.data;
  } catch (error) {
    logger.error("getPaymentTransactionsPaginated error:", { error });
    throw error;
  }
}

export async function getTransactionsByPaymentId(paymentId: string) {
  try {
    const tenantId = await getServerTenantId();
    const result = await paymentTransactionService().getTransactionsByPaymentId(
      paymentId,
      undefined,
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("getTransactionsByPaymentId error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to get transactions for payment");
    }
    
    return result.data;
  } catch (error) {
    logger.error("getTransactionsByPaymentId error:", { error });
    throw error;
  }
}

export async function getTransactionsByAthleteId(athleteId: string) {
  try {
    const tenantId = await getServerTenantId();
    const result = await paymentTransactionService().getTransactionsByAthleteId(
      athleteId,
      undefined,
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("getTransactionsByAthleteId error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to get transactions for athlete");
    }
    
    return result.data;
  } catch (error) {
    logger.error("getTransactionsByAthleteId error:", { error });
    throw error;
  }
}

export async function createPaymentTransaction(data: {
  paymentId?: string | null;
  athleteId: string;
  amount: string;
  transactionType: 'payment' | 'balance_usage' | 'refund' | 'adjustment';
  method: 'cash' | 'bank_transfer' | 'credit_card';
  description?: string | null;
  referenceNumber?: string | null;
}) {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    const result = await paymentTransactionService().createPaymentTransaction(
      data,
      undefined,
      userId?.toString(),
      tenantId || undefined
    );
    
    if (!result.success) {
      logger.error("createPaymentTransaction error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to create payment transaction");
    }
    
    return result.data;
  } catch (error) {
    logger.error("createPaymentTransaction error:", { error });
    throw error;
  }
}

export async function processPaymentFromBalance(paymentId: string, amount: string) {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    if (!tenantId) {
      throw new Error("Tenant context not found");
    }
    
    const result = await paymentTransactionService().processPaymentFromBalance(
      paymentId,
      amount,
      undefined,
      userId?.toString(),
      tenantId
    );
    
    if (!result.success) {
      logger.error("processPaymentFromBalance error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to process payment from balance");
    }
    
    return result.data;
  } catch (error) {
    logger.error("processPaymentFromBalance error:", { error });
    throw error;
  }
}

export async function deletePaymentTransaction(id: string) {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    if (!tenantId) {
      throw new Error("Tenant context not found");
    }
    
    const result = await paymentTransactionService().deletePaymentTransaction(
      id,
      undefined,
      userId?.toString(),
      tenantId
    );
    
    if (!result.success) {
      logger.error("deletePaymentTransaction error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to delete payment transaction");
    }
    
    return result.data;
  } catch (error) {
    logger.error("deletePaymentTransaction error:", { error });
    throw error;
  }
}
