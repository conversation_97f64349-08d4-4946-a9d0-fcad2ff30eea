import { pgTable, text, timestamp, integer, decimal, boolean, uuid, serial, varchar, date, time, pgEnum, bigint, unique } from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";

// Enums
export const athleteStatusEnum = pgEnum("athlete_status", ["active", "inactive", "suspended"]);
export const paymentStatusEnum = pgEnum("payment_status", ["pending", "completed", "overdue", "cancelled", "partially_paid"]);
export const paymentTypeEnum = pgEnum("payment_type", ["fee", "equipment", "other"]);
export const paymentMethodEnum = pgEnum("payment_method", ["cash", "bank_transfer", "credit_card"]);
export const transactionTypeEnum = pgEnum("transaction_type", ["payment", "balance_usage", "refund", "adjustment"]);
export const paymentPlanStatusEnum = pgEnum("payment_plan_status", ["active", "inactive"]);
export const expenseCategoryEnum = pgEnum("expense_category", ["salary", "insurance", "rent", "equipment", "other"]);
export const facilityTypeEnum = pgEnum("facility_type", ["field", "court", "pool", "studio", "other"]);
export const itemCategoryEnum = pgEnum("item_category", ["equipment", "clothing", "accessories", "other"]);
export const dimensionUnitEnum = pgEnum("dimension_unit", ["meters", "feet"]);
export const smsTypeEnum = pgEnum("sms_type", ["payment_reminder", "team_message", "custom"]);
export const smsStatusEnum = pgEnum("sms_status", ["pending", "sent", "failed", "cancelled"]);

// Base tables
export const branches = pgTable("branches", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  longDescription: text("long_description"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  createdBy: bigint("created_by", { mode: "bigint" }).notNull(), // Zitadel user ID
  updatedBy: bigint("updated_by", { mode: "bigint" }).notNull(), // Zitadel user ID
});

export const schools = pgTable("schools", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: varchar("tenant_id", { length: 255 }).notNull(), // Organization ID from Zitadel
  name: varchar("name", { length: 255 }).notNull(),
  foundedYear: integer("founded_year").notNull(),
  logo: text("logo"),
  address: text("address"),
  phone: varchar("phone", { length: 20 }),
  email: varchar("email", { length: 255 }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  createdBy: bigint("created_by", { mode: "bigint" }).notNull(), // Zitadel user ID
  updatedBy: bigint("updated_by", { mode: "bigint" }).notNull(), // Zitadel user ID
});

export const instructors = pgTable("instructors", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: varchar("tenant_id", { length: 255 }).notNull(), // Organization ID from Zitadel
  name: varchar("name", { length: 255 }).notNull(),
  surname: varchar("surname", { length: 255 }).notNull(),
  email: varchar("email", { length: 255 }).notNull(),
  phone: varchar("phone", { length: 20 }).notNull(),
  nationalId: varchar("national_id", { length: 11 }),
  birthDate: date("birth_date"),
  address: text("address"),
  salary: decimal("salary", { precision: 10, scale: 2 }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  createdBy: bigint("created_by", { mode: "bigint" }).notNull(), // Zitadel user ID
  updatedBy: bigint("updated_by", { mode: "bigint" }).notNull(), // Zitadel user ID
});

export const facilities = pgTable("facilities", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: varchar("tenant_id", { length: 255 }).notNull(), // Organization ID from Zitadel
  name: varchar("name", { length: 255 }).notNull(),
  type: facilityTypeEnum("type").notNull(),
  address: text("address").notNull(),
  totalCapacity: integer("total_capacity"),
  currentlyOccupied: integer("currently_occupied").default(0),
  length: decimal("length", { precision: 8, scale: 2 }),
  width: decimal("width", { precision: 8, scale: 2 }),
  dimensionUnit: dimensionUnitEnum("dimension_unit"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  createdBy: bigint("created_by", { mode: "bigint" }).notNull(), // Zitadel user ID
  updatedBy: bigint("updated_by", { mode: "bigint" }).notNull(), // Zitadel user ID
});

export const teams = pgTable("teams", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: varchar("tenant_id", { length: 255 }).notNull(), // Organization ID from Zitadel
  name: varchar("name", { length: 255 }).notNull(),
  schoolId: uuid("school_id").notNull().references(() => schools.id, { onDelete: "cascade" }),
  branchId: uuid("branch_id").notNull().references(() => branches.id, { onDelete: "cascade" }),
  instructorId: uuid("instructor_id").notNull().references(() => instructors.id, { onDelete: "cascade" }),
  description: text("description"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  createdBy: bigint("created_by", { mode: "bigint" }).notNull(), // Zitadel user ID
  updatedBy: bigint("updated_by", { mode: "bigint" }).notNull(), // Zitadel user ID
});

export const trainingSchedules = pgTable("training_schedules", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: varchar("tenant_id", { length: 255 }).notNull(), // Organization ID from Zitadel
  teamId: uuid("team_id").notNull().references(() => teams.id, { onDelete: "cascade" }),
  facilityId: uuid("facility_id").notNull().references(() => facilities.id, { onDelete: "cascade" }),
  dayOfWeek: integer("day_of_week").notNull(), // 0-6 (Sunday-Saturday)
  startTime: time("start_time").notNull(),
  endTime: time("end_time").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  createdBy: bigint("created_by", { mode: "bigint" }).notNull(), // Zitadel user ID
  updatedBy: bigint("updated_by", { mode: "bigint" }).notNull(), // Zitadel user ID
});

export const athletes = pgTable("athletes", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: varchar("tenant_id", { length: 255 }).notNull(), // Organization ID from Zitadel
  name: varchar("name", { length: 255 }).notNull(),
  surname: varchar("surname", { length: 255 }).notNull(),
  nationalId: varchar("national_id", { length: 11 }).notNull().unique(),
  birthDate: date("birth_date").notNull(),
  registrationDate: date("registration_date").defaultNow().notNull(),
  status: athleteStatusEnum("status").default("active").notNull(),
  balance: decimal("balance", { precision: 10, scale: 2 }).default("0").notNull(),
  // Parent information
  parentName: varchar("parent_name", { length: 255 }).notNull(),
  parentSurname: varchar("parent_surname", { length: 255 }).notNull(),
  parentPhone: varchar("parent_phone", { length: 20 }).notNull(),
  parentEmail: varchar("parent_email", { length: 255 }),
  parentAddress: text("parent_address"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  createdBy: bigint("created_by", { mode: "bigint" }).notNull(), // Zitadel user ID
  updatedBy: bigint("updated_by", { mode: "bigint" }).notNull(), // Zitadel user ID
});

export const paymentPlans = pgTable("payment_plans", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: varchar("tenant_id", { length: 255 }).notNull(), // Organization ID from Zitadel
  name: varchar("name", { length: 255 }).notNull(),
  monthlyValue: decimal("monthly_value", { precision: 10, scale: 2 }).notNull(), // Monthly payment amount
  assignDay: integer("assign_day").notNull(), // Day of month when payment is assigned (1-31)
  dueDay: integer("due_day").notNull(), // Day of month when payment is due (1-31)
  status: paymentPlanStatusEnum("status").default("active").notNull(), // Plan status
  description: text("description"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  createdBy: bigint("created_by", { mode: "bigint" }).notNull(), // Zitadel user ID
  updatedBy: bigint("updated_by", { mode: "bigint" }).notNull(), // Zitadel user ID
});

// Junction table for payment plans and branches (many-to-many relationship)
export const paymentPlanBranches = pgTable("payment_plan_branches", {
  id: uuid("id").primaryKey().defaultRandom(),
  paymentPlanId: uuid("payment_plan_id").notNull().references(() => paymentPlans.id, { onDelete: "cascade" }),
  branchId: uuid("branch_id").notNull().references(() => branches.id, { onDelete: "cascade" }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
}, (table) => ({
  // Unique constraint to prevent duplicate associations
  uniqueAssociation: unique().on(table.paymentPlanId, table.branchId),
}));

export const payments = pgTable("payments", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: varchar("tenant_id", { length: 255 }).notNull(), // Organization ID from Zitadel
  athleteId: uuid("athlete_id").notNull().references(() => athletes.id, { onDelete: "cascade" }),
  athletePaymentPlanId: uuid("athlete_payment_plan_id").references(() => athletePaymentPlans.id, { onDelete: "set null" }),
  itemPurchaseId: uuid("item_purchase_id").references(() => itemPurchases.id, { onDelete: "set null" }),
  amount: decimal("amount", { precision: 10, scale: 2 }).notNull(),
  paidAmount: decimal("paid_amount", { precision: 10, scale: 2 }).default("0").notNull(),
  date: date("date").notNull(), // Payment assigned/created date
  dueDate: date("due_date").notNull(), // When the payment is due
  status: paymentStatusEnum("status").default("pending").notNull(),
  type: paymentTypeEnum("type").notNull(),
  description: text("description"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  createdBy: bigint("created_by", { mode: "bigint" }).notNull(), // Zitadel user ID
  updatedBy: bigint("updated_by", { mode: "bigint" }).notNull(), // Zitadel user ID
});

export const paymentTransactions = pgTable("payment_transactions", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: varchar("tenant_id", { length: 255 }).notNull(), // Organization ID from Zitadel
  paymentId: uuid("payment_id").references(() => payments.id, { onDelete: "cascade" }),
  athleteId: uuid("athlete_id").notNull().references(() => athletes.id, { onDelete: "cascade" }),
  amount: decimal("amount", { precision: 10, scale: 2 }).notNull(),
  transactionType: transactionTypeEnum("transaction_type").notNull(),
  method: paymentMethodEnum("method").notNull(),
  transactionDate: timestamp("transaction_date").defaultNow().notNull(),
  description: text("description"),
  referenceNumber: varchar("reference_number", { length: 255 }), // For bank transfers, receipt numbers, etc.
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  createdBy: bigint("created_by", { mode: "bigint" }).notNull(), // Zitadel user ID
  updatedBy: bigint("updated_by", { mode: "bigint" }).notNull(), // Zitadel user ID
});

export const expenses = pgTable("expenses", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: varchar("tenant_id", { length: 255 }).notNull(), // Organization ID from Zitadel
  amount: decimal("amount", { precision: 10, scale: 2 }).notNull(),
  date: date("date").notNull(),
  category: expenseCategoryEnum("category").notNull(),
  description: text("description").notNull(),
  instructorId: uuid("instructor_id").references(() => instructors.id, { onDelete: "set null" }),
  facilityId: uuid("facility_id").references(() => facilities.id, { onDelete: "set null" }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  createdBy: bigint("created_by", { mode: "bigint" }).notNull(), // Zitadel user ID
  updatedBy: bigint("updated_by", { mode: "bigint" }).notNull(), // Zitadel user ID
});

export const items = pgTable("items", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: varchar("tenant_id", { length: 255 }).notNull(), // Organization ID from Zitadel
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  price: decimal("price", { precision: 10, scale: 2 }).notNull(),
  category: itemCategoryEnum("category").notNull(),
  stock: integer("stock").default(0).notNull(),
  image: text("image"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  createdBy: bigint("created_by", { mode: "bigint" }).notNull(), // Zitadel user ID
  updatedBy: bigint("updated_by", { mode: "bigint" }).notNull(), // Zitadel user ID
});

export const itemPurchases = pgTable("item_purchases", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: varchar("tenant_id", { length: 255 }).notNull(), // Organization ID from Zitadel
  itemId: uuid("item_id").notNull().references(() => items.id, { onDelete: "cascade" }),
  athleteId: uuid("athlete_id").notNull().references(() => athletes.id, { onDelete: "cascade" }),
  quantity: integer("quantity").notNull(),
  totalPrice: decimal("total_price", { precision: 10, scale: 2 }).notNull(),
  purchaseDate: date("purchase_date").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  createdBy: bigint("created_by", { mode: "bigint" }).notNull(), // Zitadel user ID
  updatedBy: bigint("updated_by", { mode: "bigint" }).notNull(), // Zitadel user ID
});

// Junction tables for many-to-many relationships
export const schoolBranches = pgTable("school_branches", {
  id: uuid("id").primaryKey().defaultRandom(),
  schoolId: uuid("school_id").notNull().references(() => schools.id, { onDelete: "cascade" }),
  branchId: uuid("branch_id").notNull().references(() => branches.id, { onDelete: "cascade" }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const instructorBranches = pgTable("instructor_branches", {
  id: uuid("id").primaryKey().defaultRandom(),
  instructorId: uuid("instructor_id").notNull().references(() => instructors.id, { onDelete: "cascade" }),
  branchId: uuid("branch_id").notNull().references(() => branches.id, { onDelete: "cascade" }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const instructorSchools = pgTable("instructor_schools", {
  id: uuid("id").primaryKey().defaultRandom(),
  instructorId: uuid("instructor_id").notNull().references(() => instructors.id, { onDelete: "cascade" }),
  schoolId: uuid("school_id").notNull().references(() => schools.id, { onDelete: "cascade" }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const athleteTeams = pgTable("athlete_teams", {
  id: uuid("id").primaryKey().defaultRandom(),
  athleteId: uuid("athlete_id").notNull().references(() => athletes.id, { onDelete: "cascade" }),
  teamId: uuid("team_id").notNull().references(() => teams.id, { onDelete: "cascade" }),
  joinedAt: date("joined_at").defaultNow().notNull(),
  leftAt: date("left_at"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const athletePaymentPlans = pgTable("athlete_payment_plans", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: varchar("tenant_id", { length: 255 }).notNull(), // Organization ID from Zitadel
  athleteId: uuid("athlete_id").notNull().references(() => athletes.id, { onDelete: "cascade" }),
  planId: uuid("plan_id").notNull().references(() => paymentPlans.id, { onDelete: "cascade" }),
  teamId: uuid("team_id").references(() => teams.id, { onDelete: "set null" }), // Optional: which team this plan is for
  assignedDate: date("assigned_date").defaultNow().notNull(),
  isActive: boolean("is_active").default(true).notNull(),
  lastPaymentDate: date("last_payment_date"), // Date of last generated payment
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  createdBy: bigint("created_by", { mode: "bigint" }).notNull(), // Zitadel user ID
  updatedBy: bigint("updated_by", { mode: "bigint" }).notNull(), // Zitadel user ID
});

// Relations
export const schoolsRelations = relations(schools, ({ many }) => ({
  schoolBranches: many(schoolBranches),
  instructorSchools: many(instructorSchools),
  teams: many(teams),
}));

export const branchesRelations = relations(branches, ({ many }) => ({
  schoolBranches: many(schoolBranches),
  instructorBranches: many(instructorBranches),
  teams: many(teams),
  paymentPlanBranches: many(paymentPlanBranches),
}));

export const instructorsRelations = relations(instructors, ({ many }) => ({
  instructorBranches: many(instructorBranches),
  instructorSchools: many(instructorSchools),
  teams: many(teams),
  expenses: many(expenses),
}));

export const facilitiesRelations = relations(facilities, ({ many }) => ({
  trainingSchedules: many(trainingSchedules),
  expenses: many(expenses),
}));

export const teamsRelations = relations(teams, ({ one, many }) => ({
  school: one(schools, {
    fields: [teams.schoolId],
    references: [schools.id],
  }),
  branch: one(branches, {
    fields: [teams.branchId],
    references: [branches.id],
  }),
  instructor: one(instructors, {
    fields: [teams.instructorId],
    references: [instructors.id],
  }),
  trainingSchedules: many(trainingSchedules),
  athleteTeams: many(athleteTeams),
}));

export const trainingSchedulesRelations = relations(trainingSchedules, ({ one }) => ({
  team: one(teams, {
    fields: [trainingSchedules.teamId],
    references: [teams.id],
  }),
  facility: one(facilities, {
    fields: [trainingSchedules.facilityId],
    references: [facilities.id],
  }),
}));

export const athletesRelations = relations(athletes, ({ many }) => ({
  athleteTeams: many(athleteTeams),
  payments: many(payments),
  itemPurchases: many(itemPurchases),
  athletePaymentPlans: many(athletePaymentPlans),
}));

export const paymentPlansRelations = relations(paymentPlans, ({ many }) => ({
  athletePaymentPlans: many(athletePaymentPlans),
  paymentPlanBranches: many(paymentPlanBranches),
}));

export const paymentPlanBranchesRelations = relations(paymentPlanBranches, ({ one }) => ({
  paymentPlan: one(paymentPlans, {
    fields: [paymentPlanBranches.paymentPlanId],
    references: [paymentPlans.id],
  }),
  branch: one(branches, {
    fields: [paymentPlanBranches.branchId],
    references: [branches.id],
  }),
}));

export const paymentsRelations = relations(payments, ({ one, many }) => ({
  athlete: one(athletes, {
    fields: [payments.athleteId],
    references: [athletes.id],
  }),
  athletePaymentPlan: one(athletePaymentPlans, {
    fields: [payments.athletePaymentPlanId],
    references: [athletePaymentPlans.id],
  }),
  itemPurchase: one(itemPurchases, {
    fields: [payments.itemPurchaseId],
    references: [itemPurchases.id],
  }),
  transactions: many(paymentTransactions),
}));

export const paymentTransactionsRelations = relations(paymentTransactions, ({ one }) => ({
  payment: one(payments, {
    fields: [paymentTransactions.paymentId],
    references: [payments.id],
  }),
  athlete: one(athletes, {
    fields: [paymentTransactions.athleteId],
    references: [athletes.id],
  }),
}));

export const expensesRelations = relations(expenses, ({ one }) => ({
  instructor: one(instructors, {
    fields: [expenses.instructorId],
    references: [instructors.id],
  }),
  facility: one(facilities, {
    fields: [expenses.facilityId],
    references: [facilities.id],
  }),
}));

export const itemsRelations = relations(items, ({ many }) => ({
  itemPurchases: many(itemPurchases),
}));

export const itemPurchasesRelations = relations(itemPurchases, ({ one }) => ({
  item: one(items, {
    fields: [itemPurchases.itemId],
    references: [items.id],
  }),
  athlete: one(athletes, {
    fields: [itemPurchases.athleteId],
    references: [athletes.id],
  }),
}));

// Junction table relations
export const schoolBranchesRelations = relations(schoolBranches, ({ one }) => ({
  school: one(schools, {
    fields: [schoolBranches.schoolId],
    references: [schools.id],
  }),
  branch: one(branches, {
    fields: [schoolBranches.branchId],
    references: [branches.id],
  }),
}));

export const instructorBranchesRelations = relations(instructorBranches, ({ one }) => ({
  instructor: one(instructors, {
    fields: [instructorBranches.instructorId],
    references: [instructors.id],
  }),
  branch: one(branches, {
    fields: [instructorBranches.branchId],
    references: [branches.id],
  }),
}));

export const instructorSchoolsRelations = relations(instructorSchools, ({ one }) => ({
  instructor: one(instructors, {
    fields: [instructorSchools.instructorId],
    references: [instructors.id],
  }),
  school: one(schools, {
    fields: [instructorSchools.schoolId],
    references: [schools.id],
  }),
}));

export const athleteTeamsRelations = relations(athleteTeams, ({ one }) => ({
  athlete: one(athletes, {
    fields: [athleteTeams.athleteId],
    references: [athletes.id],
  }),
  team: one(teams, {
    fields: [athleteTeams.teamId],
    references: [teams.id],
  }),
}));

export const athletePaymentPlansRelations = relations(athletePaymentPlans, ({ one, many }) => ({
  athlete: one(athletes, {
    fields: [athletePaymentPlans.athleteId],
    references: [athletes.id],
  }),
  plan: one(paymentPlans, {
    fields: [athletePaymentPlans.planId],
    references: [paymentPlans.id],
  }),
  team: one(teams, {
    fields: [athletePaymentPlans.teamId],
    references: [teams.id],
  }),
  payments: many(payments),
}));

// Export types
export type Branch = typeof branches.$inferSelect;
export type NewBranch = typeof branches.$inferInsert;

export type School = typeof schools.$inferSelect;
export type NewSchool = typeof schools.$inferInsert;

export type Instructor = typeof instructors.$inferSelect;
export type NewInstructor = typeof instructors.$inferInsert;

export type Facility = typeof facilities.$inferSelect;
export type NewFacility = typeof facilities.$inferInsert;

export type Team = typeof teams.$inferSelect;
export type NewTeam = typeof teams.$inferInsert;

export type TrainingSchedule = typeof trainingSchedules.$inferSelect;
export type NewTrainingSchedule = typeof trainingSchedules.$inferInsert;

export type Athlete = typeof athletes.$inferSelect;
export type NewAthlete = typeof athletes.$inferInsert;

export type PaymentPlan = typeof paymentPlans.$inferSelect;
export type NewPaymentPlan = typeof paymentPlans.$inferInsert;

export type Payment = typeof payments.$inferSelect;
export type NewPayment = typeof payments.$inferInsert;

export type Expense = typeof expenses.$inferSelect;
export type NewExpense = typeof expenses.$inferInsert;

export type Item = typeof items.$inferSelect;
export type NewItem = typeof items.$inferInsert;

export type ItemPurchase = typeof itemPurchases.$inferSelect;
export type NewItemPurchase = typeof itemPurchases.$inferInsert;

export type AthletePaymentPlan = typeof athletePaymentPlans.$inferSelect;
export type NewAthletePaymentPlan = typeof athletePaymentPlans.$inferInsert;

// SMS tables
export const smsConfigurations = pgTable("sms_configurations", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: varchar("tenant_id", { length: 255 }).notNull(), // Organization ID from Zitadel
  version: integer("version").notNull(),
  pendingPaymentTemplate: text("pending_payment_template").notNull(),
  overduePaymentTemplate: text("overdue_payment_template").notNull(),
  // Reminder timing configuration (days relative to due date)
  pendingReminderDays: text("pending_reminder_days").notNull(), // JSON array of days before due date: e.g., "[-5, -3, -1]"
  overdueReminderDays: text("overdue_reminder_days").notNull(), // JSON array of days after due date: e.g., "[1, 3, 5]"
  isActive: boolean("is_active").default(true).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  createdBy: bigint("created_by", { mode: "bigint" }).notNull(), // Zitadel user ID
  updatedBy: bigint("updated_by", { mode: "bigint" }).notNull(), // Zitadel user ID
});

export const smsLogs = pgTable("sms_logs", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: varchar("tenant_id", { length: 255 }).notNull(), // Organization ID from Zitadel
  type: smsTypeEnum("type").notNull(),
  status: smsStatusEnum("status").notNull(),
  receiver: varchar("receiver", { length: 20 }).notNull(),
  message: text("message").notNull(),
  senderIdentifier: varchar("sender_identifier", { length: 50 }).notNull(),
  sentAt: timestamp("sent_at").defaultNow().notNull(),
  creditsUsed: integer("credits_used").default(1).notNull(),
  providerResponse: text("provider_response"),
  // Reference to related entities
  athleteId: uuid("athlete_id").references(() => athletes.id, { onDelete: "set null" }),
  teamId: uuid("team_id").references(() => teams.id, { onDelete: "set null" }),
  // Sender information
  senderType: varchar("sender_type", { length: 20 }).notNull(), // "user" or "system"
  senderId: varchar("sender_id", { length: 255 }), // User ID if sent by user
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const smsLogPayments = pgTable("sms_log_payments", {
  id: uuid("id").primaryKey().defaultRandom(),
  smsLogId: uuid("sms_log_id").notNull().references(() => smsLogs.id, { onDelete: "cascade" }),
  paymentId: uuid("payment_id").notNull().references(() => payments.id, { onDelete: "cascade" }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
}, (table) => ({
  uniqueSmsLogPayment: unique().on(table.smsLogId, table.paymentId),
}));

export const smsBalance = pgTable("sms_balance", {
  id: uuid("id").primaryKey().defaultRandom(),
  tenantId: varchar("tenant_id", { length: 255 }).notNull().unique(), // Organization ID from Zitadel
  balance: integer("balance").default(0).notNull(),
  lastUpdated: timestamp("last_updated").defaultNow().notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  createdBy: bigint("created_by", { mode: "bigint" }).notNull(), // Zitadel user ID
  updatedBy: bigint("updated_by", { mode: "bigint" }).notNull(), // Zitadel user ID
});

export const smsPricingTiers = pgTable("sms_pricing_tiers", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: varchar("name", { length: 100 }).notNull(), // e.g., "starter", "standard", "professional", "enterprise"
  description: text("description").notNull(),
  minCredits: integer("min_credits").notNull(),
  maxCredits: integer("max_credits"), // null means unlimited
  pricePerCredit: integer("price_per_credit").notNull(), // in cents
  currency: varchar("currency", { length: 3 }).default("USD").notNull(),
  isActive: boolean("is_active").default(true).notNull(),
  sortOrder: integer("sort_order").default(0).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  createdBy: bigint("created_by", { mode: "bigint" }).notNull(), // Zitadel user ID
  updatedBy: bigint("updated_by", { mode: "bigint" }).notNull(), // Zitadel user ID
});

// SMS relations
export const smsConfigurationsRelations = relations(smsConfigurations, ({ }) => ({}));

export const smsLogsRelations = relations(smsLogs, ({ one, many }) => ({
  athlete: one(athletes, {
    fields: [smsLogs.athleteId],
    references: [athletes.id],
  }),
  team: one(teams, {
    fields: [smsLogs.teamId],
    references: [teams.id],
  }),
  smsLogPayments: many(smsLogPayments),
}));

export const smsLogPaymentsRelations = relations(smsLogPayments, ({ one }) => ({
  smsLog: one(smsLogs, {
    fields: [smsLogPayments.smsLogId],
    references: [smsLogs.id],
  }),
  payment: one(payments, {
    fields: [smsLogPayments.paymentId],
    references: [payments.id],
  }),
}));

export const smsBalanceRelations = relations(smsBalance, ({ }) => ({}));

export const smsPricingTiersRelations = relations(smsPricingTiers, ({ }) => ({}));

// Export SMS types
export type SmsConfiguration = typeof smsConfigurations.$inferSelect;
export type NewSmsConfiguration = typeof smsConfigurations.$inferInsert;

export type SmsLog = typeof smsLogs.$inferSelect;
export type NewSmsLog = typeof smsLogs.$inferInsert;

export type SmsBalance = typeof smsBalance.$inferSelect;
export type NewSmsBalance = typeof smsBalance.$inferInsert;

export type SmsPricingTier = typeof smsPricingTiers.$inferSelect;
export type NewSmsPricingTier = typeof smsPricingTiers.$inferInsert;