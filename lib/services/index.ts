// Error types and utilities
export * from '../errors/types';
export * from '../errors/errors';

// Base service class
export { BaseService } from './base';

// Validation utilities
export * from './validation';

// Service implementations
export { AthleteService } from './athlete.service';
export { PaymentPlanService } from './payment-plan.service';
export { SchoolService } from './school.service';
export { InstructorService } from './instructor.service';
export { FacilityService } from './facility.service';
export { TeamService } from './team.service';
export { ExpenseService } from './expense.service';
export { ItemService } from './item.service';
export { ItemPurchaseService } from './item-purchase.service';
export { AthleteTeamService } from './athlete-team.service';
export { PaymentService } from './payment.service';
export { PaymentTransactionService } from './payment-transaction.service';
export { BranchService } from './branch.service';
export { PaymentPlanAssignmentService } from './payment-plan-assignment.service';
export { SmsService } from './sms.service';
export { SmsManagementService } from './sms-management.service';
export { SmsLoggingService } from './sms-logging.service';
export { SmsBalanceService } from './sms-balance.service';

// Import for factory
import { AthleteService } from './athlete.service';
import { PaymentPlanService } from './payment-plan.service';
import { SchoolService } from './school.service';
import { InstructorService } from './instructor.service';
import { FacilityService } from './facility.service';
import { TeamService } from './team.service';
import { ExpenseService } from './expense.service';
import { ItemService } from './item.service';
import { ItemPurchaseService } from './item-purchase.service';
import { AthleteTeamService } from './athlete-team.service';
import { PaymentService } from './payment.service';
import { PaymentTransactionService } from './payment-transaction.service';
import { BranchService } from './branch.service';
import { FinancialService } from './financial.service';
import { PaymentPlanAssignmentService } from './payment-plan-assignment.service';
import { SmsService } from './sms.service';
import { SmsManagementService } from './sms-management.service';
import { SmsLoggingService } from './sms-logging.service';
import { SmsBalanceService } from './sms-balance.service';

// Service factory for dependency injection
export class ServiceFactory {
  private static athleteService: AthleteService;
  private static paymentPlanService: PaymentPlanService;
  private static schoolService: SchoolService;
  private static instructorService: InstructorService;
  private static facilityService: FacilityService;
  private static teamService: TeamService;
  private static expenseService: ExpenseService;
  private static itemService: ItemService;
  private static itemPurchaseService: ItemPurchaseService;
  private static athleteTeamService: AthleteTeamService;
  private static paymentService: PaymentService;
  private static paymentTransactionService: PaymentTransactionService;
  private static branchService: BranchService;
  private static financialService: FinancialService;
  private static paymentPlanAssignmentService: PaymentPlanAssignmentService;
  private static smsService: SmsService;
  private static smsManagementService: SmsManagementService;
  private static smsLoggingService: SmsLoggingService;
  private static smsBalanceService: SmsBalanceService;

  static getAthleteService(): AthleteService {
    if (!this.athleteService) {
      this.athleteService = new AthleteService();
    }
    return this.athleteService;
  }

  static getPaymentPlanService(): PaymentPlanService {
    if (!this.paymentPlanService) {
      this.paymentPlanService = new PaymentPlanService();
    }
    return this.paymentPlanService;
  }

  static getSchoolService(): SchoolService {
    if (!this.schoolService) {
      this.schoolService = new SchoolService();
    }
    return this.schoolService;
  }

  static getInstructorService(): InstructorService {
    if (!this.instructorService) {
      this.instructorService = new InstructorService();
    }
    return this.instructorService;
  }

  static getFacilityService(): FacilityService {
    if (!this.facilityService) {
      this.facilityService = new FacilityService();
    }
    return this.facilityService;
  }

  static getTeamService(): TeamService {
    if (!this.teamService) {
      this.teamService = new TeamService();
    }
    return this.teamService;
  }

  static getExpenseService(): ExpenseService {
    if (!this.expenseService) {
      this.expenseService = new ExpenseService();
    }
    return this.expenseService;
  }

  static getItemService(): ItemService {
    if (!this.itemService) {
      this.itemService = new ItemService();
    }
    return this.itemService;
  }

  static getItemPurchaseService(): ItemPurchaseService {
    if (!this.itemPurchaseService) {
      this.itemPurchaseService = new ItemPurchaseService();
    }
    return this.itemPurchaseService;
  }

  static getAthleteTeamService(): AthleteTeamService {
    if (!this.athleteTeamService) {
      this.athleteTeamService = new AthleteTeamService();
    }
    return this.athleteTeamService;
  }

  static getPaymentService(): PaymentService {
    if (!this.paymentService) {
      this.paymentService = new PaymentService();
    }
    return this.paymentService;
  }

  static getPaymentTransactionService(): PaymentTransactionService {
    if (!this.paymentTransactionService) {
      this.paymentTransactionService = new PaymentTransactionService();
    }
    return this.paymentTransactionService;
  }

  static getBranchService(): BranchService {
    if (!this.branchService) {
      this.branchService = new BranchService();
    }
    return this.branchService;
  }

  static getFinancialService(): FinancialService {
    if (!this.financialService) {
      this.financialService = new FinancialService();
    }
    return this.financialService;
  }

  static getPaymentPlanAssignmentService(): PaymentPlanAssignmentService {
    if (!this.paymentPlanAssignmentService) {
      this.paymentPlanAssignmentService = new PaymentPlanAssignmentService();
    }
    return this.paymentPlanAssignmentService;
  }

  static getSmsService(): SmsService {
    if (!this.smsService) {
      this.smsService = new SmsService();
    }
    return this.smsService;
  }

  static getSmsManagementService(): SmsManagementService {
    if (!this.smsManagementService) {
      this.smsManagementService = new SmsManagementService();
    }
    return this.smsManagementService;
  }

  static getSmsLoggingService(): SmsLoggingService {
    if (!this.smsLoggingService) {
      this.smsLoggingService = new SmsLoggingService();
    }
    return this.smsLoggingService;
  }

  static getSmsBalanceService(): SmsBalanceService {
    if (!this.smsBalanceService) {
      this.smsBalanceService = new SmsBalanceService();
    }
    return this.smsBalanceService;
  }

  // Reset services (useful for testing)
  static reset(): void {
    this.athleteService = null as any;
    this.paymentPlanService = null as any;
    this.schoolService = null as any;
    this.instructorService = null as any;
    this.facilityService = null as any;
    this.teamService = null as any;
    this.expenseService = null as any;
    this.itemService = null as any;
    this.itemPurchaseService = null as any;
    this.athleteTeamService = null as any;
    this.paymentService = null as any;
    this.paymentTransactionService = null as any;
    this.branchService = null as any;
    this.financialService = null as any;
    this.paymentPlanAssignmentService = null as any;
    this.smsService = null as any;
    this.smsManagementService = null as any;
    this.smsLoggingService = null as any;
    this.smsBalanceService = null as any;
  }
}

// Convenience functions for quick access
export const athleteService = () => ServiceFactory.getAthleteService();
export const paymentPlanService = () => ServiceFactory.getPaymentPlanService();
export const schoolService = () => ServiceFactory.getSchoolService();
export const instructorService = () => ServiceFactory.getInstructorService();
export const facilityService = () => ServiceFactory.getFacilityService();
export const teamService = () => ServiceFactory.getTeamService();
export const expenseService = () => ServiceFactory.getExpenseService();
export const itemService = () => ServiceFactory.getItemService();
export const itemPurchaseService = () => ServiceFactory.getItemPurchaseService();
export const athleteTeamService = () => ServiceFactory.getAthleteTeamService();
export const paymentService = () => ServiceFactory.getPaymentService();
export const paymentTransactionService = () => ServiceFactory.getPaymentTransactionService();
export const branchService = () => ServiceFactory.getBranchService();
export const financialService = () => ServiceFactory.getFinancialService();
export const paymentPlanAssignmentService = () => ServiceFactory.getPaymentPlanAssignmentService();
export const smsService = () => ServiceFactory.getSmsService();
export const smsManagementService = () => ServiceFactory.getSmsManagementService();
export const smsLoggingService = () => ServiceFactory.getSmsLoggingService();
export const smsBalanceService = () => ServiceFactory.getSmsBalanceService();
