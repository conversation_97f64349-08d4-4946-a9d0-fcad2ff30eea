import { eq, and, desc, count, sql, or, ilike, inArray } from 'drizzle-orm';
import { db } from '@/src/db';
import * as schema from '@/src/db/schema';
import { TenantAwareDBBase } from './base';

export class PaymentTransactionsDB extends TenantAwareDBBase {
  
  static async getPaymentTransactions(tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    return db.select({
      id: schema.paymentTransactions.id,
      paymentId: schema.paymentTransactions.paymentId,
      athleteId: schema.paymentTransactions.athleteId,
      amount: schema.paymentTransactions.amount,
      transactionType: schema.paymentTransactions.transactionType,
      method: schema.paymentTransactions.method,
      transactionDate: schema.paymentTransactions.transactionDate,
      description: schema.paymentTransactions.description,
      referenceNumber: schema.paymentTransactions.referenceNumber,
      createdAt: schema.paymentTransactions.createdAt,
      updatedAt: schema.paymentTransactions.updatedAt,
      createdBy: schema.paymentTransactions.createdBy,
      updatedBy: schema.paymentTransactions.updatedBy,
      athlete: {
        id: schema.athletes.id,
        name: schema.athletes.name,
        surname: schema.athletes.surname,
        parentName: schema.athletes.parentName,
        parentEmail: schema.athletes.parentEmail,
        parentPhone: schema.athletes.parentPhone,
      },
      payment: {
        id: schema.payments.id,
        amount: schema.payments.amount,
        type: schema.payments.type,
        description: schema.payments.description,
      }
    })
    .from(schema.paymentTransactions)
    .leftJoin(schema.athletes, eq(schema.paymentTransactions.athleteId, schema.athletes.id))
    .leftJoin(schema.payments, eq(schema.paymentTransactions.paymentId, schema.payments.id))
    .where(eq(schema.paymentTransactions.tenantId, filter.tenantId))
    .orderBy(desc(schema.paymentTransactions.transactionDate));
  }

  static async getPaymentTransactionsPaginated(
    page: number = 1,
    limit: number = 10,
    search?: string,
    sortBy: string = 'transactionDate',
    sortOrder: 'asc' | 'desc' = 'desc',
    filters: Record<string, string> = {},
    tenantId?: string
  ) {
    const filter = await this.getTenantFilter(tenantId);
    const offset = (page - 1) * limit;

    // Build WHERE conditions
    const conditions = [eq(schema.paymentTransactions.tenantId, filter.tenantId)];

    // Add search conditions
    if (search && search.length >= 3) {
      const searchConditions = [
        ilike(schema.athletes.name, `%${search}%`),
        ilike(schema.athletes.surname, `%${search}%`),
        ilike(schema.paymentTransactions.description, `%${search}%`),
        ilike(schema.paymentTransactions.referenceNumber, `%${search}%`),
      ];
      
      // Try to parse search as number for amount search
      const numericSearch = parseFloat(search);
      if (!isNaN(numericSearch)) {
        searchConditions.push(
          sql`CAST(${schema.paymentTransactions.amount} AS DECIMAL) = ${numericSearch}`
        );
      }
      
      conditions.push(or(...searchConditions)!);
    }

    // Add filters
    if (filters.transactionType) {
      conditions.push(eq(schema.paymentTransactions.transactionType, filters.transactionType as any));
    }
    if (filters.method) {
      conditions.push(eq(schema.paymentTransactions.method, filters.method as any));
    }
    if (filters.athleteId) {
      conditions.push(eq(schema.paymentTransactions.athleteId, filters.athleteId));
    }
    if (filters.paymentId) {
      conditions.push(eq(schema.paymentTransactions.paymentId, filters.paymentId));
    }
    if (filters.fromDate) {
      conditions.push(sql`${schema.paymentTransactions.transactionDate} >= ${filters.fromDate}`);
    }
    if (filters.toDate) {
      conditions.push(sql`${schema.paymentTransactions.transactionDate} <= ${filters.toDate}`);
    }

    const whereClause = and(...conditions);

    // Build ORDER BY clause
    let orderByClause;
    switch (sortBy) {
      case 'amount':
        orderByClause = sortOrder === 'asc' ? schema.paymentTransactions.amount : desc(schema.paymentTransactions.amount);
        break;
      case 'transactionType':
        orderByClause = sortOrder === 'asc' ? schema.paymentTransactions.transactionType : desc(schema.paymentTransactions.transactionType);
        break;
      case 'method':
        orderByClause = sortOrder === 'asc' ? schema.paymentTransactions.method : desc(schema.paymentTransactions.method);
        break;
      case 'createdAt':
        orderByClause = sortOrder === 'asc' ? schema.paymentTransactions.createdAt : desc(schema.paymentTransactions.createdAt);
        break;
      case 'transactionDate':
      default:
        orderByClause = sortOrder === 'asc' ? schema.paymentTransactions.transactionDate : desc(schema.paymentTransactions.transactionDate);
        break;
    }

    // Get total count
    const totalResult = await db
      .select({ count: count() })
      .from(schema.paymentTransactions)
      .leftJoin(schema.athletes, eq(schema.paymentTransactions.athleteId, schema.athletes.id))
      .leftJoin(schema.payments, eq(schema.paymentTransactions.paymentId, schema.payments.id))
      .where(whereClause);

    const total = totalResult[0]?.count || 0;

    // Get paginated data
    const data = await db.select({
      id: schema.paymentTransactions.id,
      paymentId: schema.paymentTransactions.paymentId,
      athleteId: schema.paymentTransactions.athleteId,
      amount: schema.paymentTransactions.amount,
      transactionType: schema.paymentTransactions.transactionType,
      method: schema.paymentTransactions.method,
      transactionDate: schema.paymentTransactions.transactionDate,
      description: schema.paymentTransactions.description,
      referenceNumber: schema.paymentTransactions.referenceNumber,
      createdAt: schema.paymentTransactions.createdAt,
      updatedAt: schema.paymentTransactions.updatedAt,
      createdBy: schema.paymentTransactions.createdBy,
      updatedBy: schema.paymentTransactions.updatedBy,
      athlete: {
        id: schema.athletes.id,
        name: schema.athletes.name,
        surname: schema.athletes.surname,
        parentName: schema.athletes.parentName,
        parentEmail: schema.athletes.parentEmail,
        parentPhone: schema.athletes.parentPhone,
      },
      payment: {
        id: schema.payments.id,
        amount: schema.payments.amount,
        type: schema.payments.type,
        description: schema.payments.description,
      }
    })
    .from(schema.paymentTransactions)
    .leftJoin(schema.athletes, eq(schema.paymentTransactions.athleteId, schema.athletes.id))
    .leftJoin(schema.payments, eq(schema.paymentTransactions.paymentId, schema.payments.id))
    .where(whereClause)
    .orderBy(orderByClause)
    .limit(limit)
    .offset(offset);

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  static async getPaymentTransactionById(id: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    const result = await db.select({
      id: schema.paymentTransactions.id,
      paymentId: schema.paymentTransactions.paymentId,
      athleteId: schema.paymentTransactions.athleteId,
      amount: schema.paymentTransactions.amount,
      transactionType: schema.paymentTransactions.transactionType,
      method: schema.paymentTransactions.method,
      transactionDate: schema.paymentTransactions.transactionDate,
      description: schema.paymentTransactions.description,
      referenceNumber: schema.paymentTransactions.referenceNumber,
      createdAt: schema.paymentTransactions.createdAt,
      updatedAt: schema.paymentTransactions.updatedAt,
      createdBy: schema.paymentTransactions.createdBy,
      updatedBy: schema.paymentTransactions.updatedBy,
      athlete: {
        id: schema.athletes.id,
        name: schema.athletes.name,
        surname: schema.athletes.surname,
        parentName: schema.athletes.parentName,
        parentEmail: schema.athletes.parentEmail,
        parentPhone: schema.athletes.parentPhone,
      },
      payment: {
        id: schema.payments.id,
        amount: schema.payments.amount,
        type: schema.payments.type,
        description: schema.payments.description,
      }
    })
    .from(schema.paymentTransactions)
    .leftJoin(schema.athletes, eq(schema.paymentTransactions.athleteId, schema.athletes.id))
    .leftJoin(schema.payments, eq(schema.paymentTransactions.paymentId, schema.payments.id))
    .where(and(
      eq(schema.paymentTransactions.id, id),
      eq(schema.paymentTransactions.tenantId, filter.tenantId)
    ));
    
    return result[0] || null;
  }

  static async getTransactionsByPaymentId(paymentId: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    return db.select({
      id: schema.paymentTransactions.id,
      paymentId: schema.paymentTransactions.paymentId,
      athleteId: schema.paymentTransactions.athleteId,
      amount: schema.paymentTransactions.amount,
      transactionType: schema.paymentTransactions.transactionType,
      method: schema.paymentTransactions.method,
      transactionDate: schema.paymentTransactions.transactionDate,
      description: schema.paymentTransactions.description,
      referenceNumber: schema.paymentTransactions.referenceNumber,
      createdAt: schema.paymentTransactions.createdAt,
      updatedAt: schema.paymentTransactions.updatedAt,
      createdBy: schema.paymentTransactions.createdBy,
      updatedBy: schema.paymentTransactions.updatedBy,
    })
    .from(schema.paymentTransactions)
    .where(and(
      eq(schema.paymentTransactions.paymentId, paymentId),
      eq(schema.paymentTransactions.tenantId, filter.tenantId)
    ))
    .orderBy(desc(schema.paymentTransactions.transactionDate));
  }

  static async getTransactionsByAthleteId(athleteId: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    return db.select({
      id: schema.paymentTransactions.id,
      paymentId: schema.paymentTransactions.paymentId,
      athleteId: schema.paymentTransactions.athleteId,
      amount: schema.paymentTransactions.amount,
      transactionType: schema.paymentTransactions.transactionType,
      method: schema.paymentTransactions.method,
      transactionDate: schema.paymentTransactions.transactionDate,
      description: schema.paymentTransactions.description,
      referenceNumber: schema.paymentTransactions.referenceNumber,
      createdAt: schema.paymentTransactions.createdAt,
      updatedAt: schema.paymentTransactions.updatedAt,
      createdBy: schema.paymentTransactions.createdBy,
      updatedBy: schema.paymentTransactions.updatedBy,
      payment: {
        id: schema.payments.id,
        amount: schema.payments.amount,
        type: schema.payments.type,
        description: schema.payments.description,
      }
    })
    .from(schema.paymentTransactions)
    .leftJoin(schema.payments, eq(schema.paymentTransactions.paymentId, schema.payments.id))
    .where(and(
      eq(schema.paymentTransactions.athleteId, athleteId),
      eq(schema.paymentTransactions.tenantId, filter.tenantId)
    ))
    .orderBy(desc(schema.paymentTransactions.transactionDate));
  }

  static async createPaymentTransaction(
    data: {
      paymentId?: string | null;
      athleteId: string;
      amount: string;
      transactionType: 'payment' | 'balance_usage' | 'refund' | 'adjustment';
      method: 'cash' | 'bank_transfer' | 'credit_card';
      description?: string | null;
      referenceNumber?: string | null;
    },
    tenantId?: string,
    userId?: bigint
  ) {
    const filter = await this.getTenantFilter(tenantId);
    const result = await db.insert(schema.paymentTransactions).values({
      tenantId: filter.tenantId,
      paymentId: data.paymentId,
      athleteId: data.athleteId,
      amount: data.amount,
      transactionType: data.transactionType,
      method: data.method,
      description: data.description,
      referenceNumber: data.referenceNumber,
      createdBy: userId || BigInt(1),
      updatedBy: userId || BigInt(1),
    }).returning();

    return result[0];
  }

  static async deletePaymentTransaction(id: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    await db.delete(schema.paymentTransactions)
      .where(and(
        eq(schema.paymentTransactions.id, id),
        eq(schema.paymentTransactions.tenantId, filter.tenantId)
      ));
    return true;
  }
}
